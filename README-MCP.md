# Cocos Creator AI集成 - MCP服务器

本项目现在支持通过MCP（Model Context Protocol）协议与AI大模型进行深度集成，让AI能够理解和操作Cocos Creator项目。

## 🚀 快速开始

### 1. 启动MCP服务器

```bash
# 使用stdio模式（推荐用于AI客户端）
cd mcp-server
npm run start:stdio

# 或使用HTTP模式（用于Web集成）
npm run start:http
```

### 2. 配置AI客户端

#### Claude Desktop配置

1. 找到Claude Desktop配置文件：
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - Linux: `~/.config/Claude/claude_desktop_config.json`

2. 添加MCP服务器配置：
```json
{
  "mcpServers": {
    "cocos-creator": {
      "command": "node",
      "args": ["/path/to/your/project/mcp-server/dist/index.js"],
      "cwd": "/path/to/your/project"
    }
  }
}
```

3. 重启Claude Desktop

## 🎯 功能特性

### 📁 资源访问
- **项目信息**: `cocos://project/info` - 获取项目基本信息和结构
- **场景管理**: `cocos://scenes/list` - 查看所有场景
- **场景详情**: `cocos://scenes/{sceneName}` - 获取特定场景信息
- **资源浏览**: `cocos://assets/{assetType}` - 按类型浏览资源
- **代码访问**: `cocos://code/{filePath}` - 读取源代码文件
- **项目设置**: `cocos://config/settings` - 查看项目配置

### 🔧 开发工具
- **create-file** - 创建新文件
- **read-file** - 读取文件内容
- **modify-file** - 修改文件内容
- **create-scene** - 创建新场景
- **analyze-scene** - 分析场景结构
- **generate-component** - 生成组件代码
- **project-health-check** - 项目健康检查
- **analyze-code** - 代码分析

### 💡 智能提示
- **develop-component** - 组件开发指导
- **design-scene** - 场景设计指导
- **implement-game-logic** - 游戏逻辑实现
- **debug-performance** - 性能调试
- **diagnose-error** - 错误诊断
- **optimize-performance** - 性能优化
- **optimize-assets** - 资源优化
- **learning-path** - 学习路径规划
- **best-practices** - 最佳实践

## 💬 使用示例

### 项目分析
```
请帮我分析当前Cocos Creator项目的结构和健康状况
```

### 组件开发
```
帮我创建一个PlayerController组件，包含移动、跳跃和攻击功能
```

### 场景设计
```
我想设计一个2D平台游戏的主菜单场景，请给我一些建议
```

### 性能优化
```
我的游戏在移动设备上运行缓慢，请帮我分析和优化性能
```

### 错误调试
```
我遇到了"Cannot read property of undefined"错误，请帮我诊断问题
```

## 🛠️ 开发和扩展

### 项目结构
```
mcp-server/
├── src/
│   ├── index.ts              # 主服务器入口
│   └── providers/
│       ├── resources.ts      # 资源提供者
│       ├── tools.ts         # 工具提供者
│       └── prompts.ts       # 提示提供者
├── dist/                    # 编译输出
├── package.json
├── tsconfig.json
├── start.sh                 # 启动脚本
├── test-server.js          # 测试脚本
└── README.md
```

### 测试服务器
```bash
cd mcp-server
node test-server.js
```

### 添加新功能
1. **新资源**: 在 `resources.ts` 中注册新的资源类型
2. **新工具**: 在 `tools.ts` 中实现新的操作工具
3. **新提示**: 在 `prompts.ts` 中创建新的提示模板

## 🔧 故障排除

### 常见问题

1. **连接失败**
   - 检查Node.js版本（需要18.0.0+）
   - 确保在项目根目录运行
   - 检查文件权限

2. **路径错误**
   - 使用绝对路径配置
   - 确保cwd指向项目根目录

3. **权限问题**
   - 确保对项目目录有读写权限
   - 检查脚本执行权限

### 调试模式
```bash
# 启用详细日志
NODE_ENV=development npm run start:stdio

# 查看服务器日志
npm run start:stdio 2> server.log
```

## 📚 更多信息

- [MCP协议文档](https://modelcontextprotocol.io/)
- [Cocos Creator文档](https://docs.cocos.com/)
- [项目详细文档](mcp-server/README.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进MCP集成功能！

## 📄 许可证

MIT License

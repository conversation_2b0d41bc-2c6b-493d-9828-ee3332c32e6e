#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import express from "express";
import { randomUUID } from "node:crypto";
import { CocosResourceProvider } from "./providers/resources.js";
import { CocosToolProvider } from "./providers/tools.js";
import { CocosPromptProvider } from "./providers/prompts.js";

/**
 * Cocos Creator MCP Server
 * 为AI大模型提供Cocos Creator项目的上下文和操作能力
 */
class CocosMcpServer {
  private server: McpServer;
  private resourceProvider: CocosResourceProvider;
  private toolProvider: CocosToolProvider;
  private promptProvider: CocosPromptProvider;

  constructor() {
    this.server = new McpServer({
      name: "cocos-mcp-server",
      version: "1.0.0"
    });

    // 初始化提供者
    this.resourceProvider = new CocosResourceProvider();
    this.toolProvider = new CocosToolProvider();
    this.promptProvider = new CocosPromptProvider();

    this.setupProviders();
  }

  /**
   * 设置所有提供者
   */
  private setupProviders(): void {
    this.resourceProvider.register(this.server);
    this.toolProvider.register(this.server);
    this.promptProvider.register(this.server);
  }

  /**
   * 启动stdio传输
   */
  async startStdio(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Cocos MCP Server running on stdio");
  }

  /**
   * 启动HTTP传输
   */
  async startHttp(port: number = 3000): Promise<void> {
    const app = express();
    app.use(express.json());

    // 存储传输实例
    const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

    // 处理MCP请求
    app.post('/mcp', async (req, res) => {
      const sessionId = req.headers['mcp-session-id'] as string | undefined;
      let transport: StreamableHTTPServerTransport;

      if (sessionId && transports[sessionId]) {
        transport = transports[sessionId];
      } else {
        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => randomUUID(),
          onsessioninitialized: (sessionId) => {
            transports[sessionId] = transport;
          },
          enableDnsRebindingProtection: true,
          allowedHosts: ['127.0.0.1', 'localhost']
        });

        transport.onclose = () => {
          if (transport.sessionId) {
            delete transports[transport.sessionId];
          }
        };

        await this.server.connect(transport);
      }

      await transport.handleRequest(req, res, req.body);
    });

    // 处理SSE连接
    app.get('/mcp', async (req, res) => {
      const sessionId = req.headers['mcp-session-id'] as string | undefined;
      if (!sessionId || !transports[sessionId]) {
        res.status(400).send('Invalid or missing session ID');
        return;
      }
      
      const transport = transports[sessionId];
      await transport.handleRequest(req, res);
    });

    // 处理会话终止
    app.delete('/mcp', async (req, res) => {
      const sessionId = req.headers['mcp-session-id'] as string | undefined;
      if (!sessionId || !transports[sessionId]) {
        res.status(400).send('Invalid or missing session ID');
        return;
      }
      
      const transport = transports[sessionId];
      await transport.handleRequest(req, res);
    });

    app.listen(port, () => {
      console.error(`Cocos MCP Server running on http://localhost:${port}/mcp`);
    });
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const transportType = args.includes('--transport') ? args[args.indexOf('--transport') + 1] : 'stdio';
  const port = args.includes('--port') ? parseInt(args[args.indexOf('--port') + 1]) : 3000;

  const server = new CocosMcpServer();

  try {
    if (transportType === 'http') {
      await server.startHttp(port);
    } else {
      await server.startStdio();
    }
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { CocosMcpServer };

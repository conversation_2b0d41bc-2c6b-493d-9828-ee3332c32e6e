import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { writeFileSync, readFileSync, existsSync, mkdirSync } from "fs";
import { join, dirname } from "path";
import { z } from "zod";

/**
 * Cocos Creator工具提供者
 * 提供创建、修改、分析Cocos项目的工具
 */
export class CocosToolProvider {
  private projectRoot: string;

  constructor(projectRoot: string = process.cwd()) {
    this.projectRoot = projectRoot;
  }

  /**
   * 注册所有工具到MCP服务器
   */
  register(server: McpServer): void {
    this.registerFileOperations(server);
    this.registerSceneOperations(server);
    this.registerCodeGeneration(server);
    this.registerProjectAnalysis(server);
  }

  /**
   * 注册文件操作工具
   */
  private registerFileOperations(server: McpServer): void {
    // 创建文件工具
    server.registerTool(
      "create-file",
      {
        title: "Create File",
        description: "Create a new file in the project",
        inputSchema: {
          path: z.string().describe("File path relative to project root"),
          content: z.string().describe("File content"),
          overwrite: z.boolean().optional().describe("Whether to overwrite existing file")
        }
      },
      async ({ path, content, overwrite = false }) => {
        const fullPath = join(this.projectRoot, path);
        
        if (existsSync(fullPath) && !overwrite) {
          return {
            content: [{
              type: "text",
              text: `File already exists: ${path}. Use overwrite=true to replace it.`
            }],
            isError: true
          };
        }

        try {
          // 确保目录存在
          const dir = dirname(fullPath);
          if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
          }

          writeFileSync(fullPath, content, 'utf-8');
          
          return {
            content: [{
              type: "text",
              text: `Successfully created file: ${path}`
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to create file: ${error}`
            }],
            isError: true
          };
        }
      }
    );

    // 读取文件工具
    server.registerTool(
      "read-file",
      {
        title: "Read File",
        description: "Read content of a file",
        inputSchema: {
          path: z.string().describe("File path relative to project root")
        }
      },
      async ({ path }) => {
        const fullPath = join(this.projectRoot, path);
        
        if (!existsSync(fullPath)) {
          return {
            content: [{
              type: "text",
              text: `File not found: ${path}`
            }],
            isError: true
          };
        }

        try {
          const content = readFileSync(fullPath, 'utf-8');
          return {
            content: [{
              type: "text",
              text: content
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to read file: ${error}`
            }],
            isError: true
          };
        }
      }
    );

    // 修改文件工具
    server.registerTool(
      "modify-file",
      {
        title: "Modify File",
        description: "Modify content of an existing file",
        inputSchema: {
          path: z.string().describe("File path relative to project root"),
          content: z.string().describe("New file content"),
          backup: z.boolean().optional().describe("Whether to create backup")
        }
      },
      async ({ path, content, backup = true }) => {
        const fullPath = join(this.projectRoot, path);
        
        if (!existsSync(fullPath)) {
          return {
            content: [{
              type: "text",
              text: `File not found: ${path}`
            }],
            isError: true
          };
        }

        try {
          // 创建备份
          if (backup) {
            const backupPath = `${fullPath}.backup.${Date.now()}`;
            const originalContent = readFileSync(fullPath, 'utf-8');
            writeFileSync(backupPath, originalContent, 'utf-8');
          }

          writeFileSync(fullPath, content, 'utf-8');
          
          return {
            content: [{
              type: "text",
              text: `Successfully modified file: ${path}${backup ? ' (backup created)' : ''}`
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to modify file: ${error}`
            }],
            isError: true
          };
        }
      }
    );
  }

  /**
   * 注册场景操作工具
   */
  private registerSceneOperations(server: McpServer): void {
    // 创建场景工具
    server.registerTool(
      "create-scene",
      {
        title: "Create Scene",
        description: "Create a new Cocos Creator scene",
        inputSchema: {
          name: z.string().describe("Scene name"),
          template: z.enum(["empty", "2d", "ui"]).optional().describe("Scene template type")
        }
      },
      async ({ name, template = "empty" }) => {
        const scenePath = join(this.projectRoot, "assets", "Scene", `${name}.scene`);
        
        if (existsSync(scenePath)) {
          return {
            content: [{
              type: "text",
              text: `Scene already exists: ${name}`
            }],
            isError: true
          };
        }

        try {
          const sceneContent = this.generateSceneTemplate(name, template);
          
          // 确保Scene目录存在
          const sceneDir = dirname(scenePath);
          if (!existsSync(sceneDir)) {
            mkdirSync(sceneDir, { recursive: true });
          }

          writeFileSync(scenePath, JSON.stringify(sceneContent, null, 2), 'utf-8');
          
          return {
            content: [{
              type: "text",
              text: `Successfully created scene: ${name} (${template} template)`
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to create scene: ${error}`
            }],
            isError: true
          };
        }
      }
    );

    // 分析场景工具
    server.registerTool(
      "analyze-scene",
      {
        title: "Analyze Scene",
        description: "Analyze scene structure and components",
        inputSchema: {
          sceneName: z.string().describe("Scene name to analyze")
        }
      },
      async ({ sceneName }) => {
        const scenePath = join(this.projectRoot, "assets", "Scene", `${sceneName}.scene`);
        
        if (!existsSync(scenePath)) {
          return {
            content: [{
              type: "text",
              text: `Scene not found: ${sceneName}`
            }],
            isError: true
          };
        }

        try {
          const sceneContent = readFileSync(scenePath, 'utf-8');
          const sceneData = JSON.parse(sceneContent);
          const analysis = this.analyzeSceneData(sceneData);
          
          return {
            content: [{
              type: "text",
              text: JSON.stringify(analysis, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to analyze scene: ${error}`
            }],
            isError: true
          };
        }
      }
    );
  }

  /**
   * 注册代码生成工具
   */
  private registerCodeGeneration(server: McpServer): void {
    // 生成组件工具
    server.registerTool(
      "generate-component",
      {
        title: "Generate Component",
        description: "Generate a new Cocos Creator component",
        inputSchema: {
          name: z.string().describe("Component name"),
          type: z.enum(["basic", "ui", "game"]).optional().describe("Component type"),
          properties: z.array(z.object({
            name: z.string(),
            type: z.string(),
            default: z.any().optional()
          })).optional().describe("Component properties")
        }
      },
      async ({ name, type = "basic", properties = [] }) => {
        const componentPath = join(this.projectRoot, "assets", "Code", `${name}.ts`);
        
        if (existsSync(componentPath)) {
          return {
            content: [{
              type: "text",
              text: `Component already exists: ${name}`
            }],
            isError: true
          };
        }

        try {
          const componentCode = this.generateComponentCode(name, type, properties);
          
          // 确保Code目录存在
          const codeDir = dirname(componentPath);
          if (!existsSync(codeDir)) {
            mkdirSync(codeDir, { recursive: true });
          }

          writeFileSync(componentPath, componentCode, 'utf-8');
          
          return {
            content: [{
              type: "text",
              text: `Successfully generated component: ${name} (${type} type)`
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to generate component: ${error}`
            }],
            isError: true
          };
        }
      }
    );
  }

  /**
   * 注册项目分析工具
   */
  private registerProjectAnalysis(server: McpServer): void {
    // 项目健康检查工具
    server.registerTool(
      "project-health-check",
      {
        title: "Project Health Check",
        description: "Analyze project structure and identify potential issues",
        inputSchema: {}
      },
      async () => {
        try {
          const healthReport = this.performHealthCheck();
          
          return {
            content: [{
              type: "text",
              text: JSON.stringify(healthReport, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to perform health check: ${error}`
            }],
            isError: true
          };
        }
      }
    );

    // 代码分析工具
    server.registerTool(
      "analyze-code",
      {
        title: "Analyze Code",
        description: "Analyze TypeScript/JavaScript code for patterns and issues",
        inputSchema: {
          filePath: z.string().describe("Code file path to analyze")
        }
      },
      async ({ filePath }) => {
        const fullPath = join(this.projectRoot, filePath);
        
        if (!existsSync(fullPath)) {
          return {
            content: [{
              type: "text",
              text: `File not found: ${filePath}`
            }],
            isError: true
          };
        }

        try {
          const code = readFileSync(fullPath, 'utf-8');
          const analysis = this.analyzeCode(code, filePath);
          
          return {
            content: [{
              type: "text",
              text: JSON.stringify(analysis, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Failed to analyze code: ${error}`
            }],
            isError: true
          };
        }
      }
    );
  }

  /**
   * 生成场景模板
   */
  private generateSceneTemplate(name: string, template: string): any {
    const baseScene = {
      "__type__": "cc.SceneAsset",
      "_name": name,
      "_objFlags": 0,
      "_native": "",
      "scene": {
        "__type__": "cc.Scene",
        "_name": name,
        "_objFlags": 0,
        "_parent": null,
        "_children": [],
        "_active": true,
        "_components": [],
        "_prefab": null,
        "autoReleaseAssets": false
      }
    };

    // 根据模板类型添加默认节点
    switch (template) {
      case "2d":
        // 添加2D相关的默认节点
        break;
      case "ui":
        // 添加UI相关的默认节点
        break;
      default:
        // 空场景
        break;
    }

    return baseScene;
  }

  /**
   * 分析场景数据
   */
  private analyzeSceneData(sceneData: any): any {
    const analysis = {
      nodeCount: 0,
      componentTypes: new Set<string>(),
      maxDepth: 0,
      issues: [] as string[]
    };

    const analyzeNode = (node: any, depth: number = 0) => {
      if (!node) return;
      
      analysis.nodeCount++;
      analysis.maxDepth = Math.max(analysis.maxDepth, depth);
      
      // 分析组件
      if (node._components) {
        for (const comp of node._components) {
          if (comp.__type__) {
            analysis.componentTypes.add(comp.__type__);
          }
        }
      }
      
      // 递归分析子节点
      if (node._children) {
        for (const child of node._children) {
          analyzeNode(child, depth + 1);
        }
      }
    };

    if (sceneData.scene) {
      analyzeNode(sceneData.scene);
    }

    return {
      ...analysis,
      componentTypes: Array.from(analysis.componentTypes)
    };
  }

  /**
   * 生成组件代码
   */
  private generateComponentCode(name: string, type: string, properties: any[]): string {
    const imports = ['import { _decorator, Component, Node } from "cc";'];
    const decorators = ['const { ccclass, property } = _decorator;'];
    
    let classContent = `@ccclass('${name}')\nexport class ${name} extends Component {\n`;
    
    // 添加属性
    for (const prop of properties) {
      classContent += `    @property(${prop.type})\n`;
      classContent += `    ${prop.name}: ${prop.type}${prop.default !== undefined ? ` = ${JSON.stringify(prop.default)}` : ''};\n\n`;
    }
    
    // 添加生命周期方法
    classContent += `    start() {\n        // 初始化逻辑\n    }\n\n`;
    classContent += `    update(deltaTime: number) {\n        // 更新逻辑\n    }\n`;
    classContent += `}`;
    
    return [...imports, '', ...decorators, '', classContent].join('\n');
  }

  /**
   * 执行健康检查
   */
  private performHealthCheck(): any {
    const report = {
      timestamp: new Date().toISOString(),
      status: "healthy",
      issues: [] as string[],
      warnings: [] as string[],
      suggestions: [] as string[]
    };

    // 检查必要文件
    const requiredFiles = ['package.json', 'tsconfig.json'];
    for (const file of requiredFiles) {
      if (!existsSync(join(this.projectRoot, file))) {
        report.issues.push(`Missing required file: ${file}`);
      }
    }

    // 检查目录结构
    const requiredDirs = ['assets', 'settings'];
    for (const dir of requiredDirs) {
      if (!existsSync(join(this.projectRoot, dir))) {
        report.issues.push(`Missing required directory: ${dir}`);
      }
    }

    if (report.issues.length > 0) {
      report.status = "unhealthy";
    } else if (report.warnings.length > 0) {
      report.status = "warning";
    }

    return report;
  }

  /**
   * 分析代码
   */
  private analyzeCode(code: string, filePath: string): any {
    const analysis = {
      filePath,
      lineCount: code.split('\n').length,
      characterCount: code.length,
      imports: [] as string[],
      classes: [] as string[],
      functions: [] as string[],
      issues: [] as string[],
      suggestions: [] as string[]
    };

    // 简单的代码分析
    const lines = code.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // 分析导入
      if (trimmed.startsWith('import ')) {
        analysis.imports.push(trimmed);
      }
      
      // 分析类
      if (trimmed.includes('class ')) {
        const match = trimmed.match(/class\s+(\w+)/);
        if (match) {
          analysis.classes.push(match[1]);
        }
      }
      
      // 分析函数
      if (trimmed.includes('function ') || trimmed.match(/\w+\s*\(/)) {
        const match = trimmed.match(/(?:function\s+)?(\w+)\s*\(/);
        if (match && !analysis.functions.includes(match[1])) {
          analysis.functions.push(match[1]);
        }
      }
    }

    return analysis;
  }
}

import {
	McpServer,
	ResourceTemplate,
} from '@modelcontextprotocol/sdk/server/mcp.js';
import { readFileSync, existsSync, readdirSync, statSync } from 'fs';
import { join, extname, relative } from 'path';
import { z } from 'zod';

/**
 * Cocos Creator资源提供者
 * 提供项目文件、场景、组件等信息的访问
 */
export class CocosResourceProvider {
	private projectRoot: string;

	constructor(projectRoot: string = process.cwd()) {
		this.projectRoot = projectRoot;
	}

	/**
	 * 注册所有资源到MCP服务器
	 */
	register(server: McpServer): void {
		this.registerProjectInfo(server);
		this.registerSceneResources(server);
		this.registerAssetResources(server);
		this.registerCodeResources(server);
		this.registerConfigResources(server);
	}

	/**
	 * 注册项目信息资源
	 */
	private registerProjectInfo(server: McpServer): void {
		server.registerResource(
			'project-info',
			'cocos://project/info',
			{
				title: 'Project Information',
				description: 'Cocos Creator project basic information',
				mimeType: 'application/json',
			},
			async (uri) => {
				const packageJsonPath = join(this.projectRoot, 'package.json');
				const projectInfo: any = {
					name: 'Unknown Project',
					version: '1.0.0',
					creator: { version: 'Unknown' },
				};

				if (existsSync(packageJsonPath)) {
					try {
						const packageJson = JSON.parse(
							readFileSync(packageJsonPath, 'utf-8')
						);
						Object.assign(projectInfo, packageJson);
					} catch (error) {
						console.error('Failed to read package.json:', error);
					}
				}

				// 获取项目结构信息
				const structure = this.getProjectStructure();

				return {
					contents: [
						{
							uri: uri.href,
							text: JSON.stringify(
								{
									...projectInfo,
									structure,
									timestamp: new Date().toISOString(),
								},
								null,
								2
							),
							mimeType: 'application/json',
						},
					],
				};
			}
		);
	}

	/**
	 * 注册场景资源
	 */
	private registerSceneResources(server: McpServer): void {
		server.registerResource(
			'scene-list',
			'cocos://scenes/list',
			{
				title: 'Scene List',
				description: 'List of all scenes in the project',
				mimeType: 'application/json',
			},
			async (uri) => {
				const scenes = this.getSceneList();
				return {
					contents: [
						{
							uri: uri.href,
							text: JSON.stringify(scenes, null, 2),
							mimeType: 'application/json',
						},
					],
				};
			}
		);

		server.registerResource(
			'scene-detail',
			new ResourceTemplate('cocos://scenes/{sceneName}', { list: undefined }),
			{
				title: 'Scene Details',
				description: 'Detailed information about a specific scene',
			},
			async (uri, { sceneName }) => {
				const sceneInfo = this.getSceneInfo(sceneName as string);
				return {
					contents: [
						{
							uri: uri.href,
							text: JSON.stringify(sceneInfo, null, 2),
							mimeType: 'application/json',
						},
					],
				};
			}
		);
	}

	/**
	 * 注册资源文件
	 */
	private registerAssetResources(server: McpServer): void {
		server.registerResource(
			'asset-list',
			new ResourceTemplate('cocos://assets/{assetType}', {
				list: undefined,
				complete: {
					assetType: (value) => {
						return [
							'images',
							'audio',
							'prefabs',
							'scripts',
							'fonts',
							'materials',
						].filter((type) => type.startsWith(value));
					},
				},
			}),
			{
				title: 'Asset List',
				description: 'List assets by type',
			},
			async (uri, { assetType }) => {
				const assets = this.getAssetsByType(assetType as string);
				return {
					contents: [
						{
							uri: uri.href,
							text: JSON.stringify(assets, null, 2),
							mimeType: 'application/json',
						},
					],
				};
			}
		);
	}

	/**
	 * 注册代码资源
	 */
	private registerCodeResources(server: McpServer): void {
		server.registerResource(
			'code-file',
			new ResourceTemplate('cocos://code/{filePath}', { list: undefined }),
			{
				title: 'Code File',
				description: 'TypeScript/JavaScript source code files',
			},
			async (uri, { filePath }) => {
				const fullPath = join(
					this.projectRoot,
					'assets',
					'Code',
					filePath as string
				);
				if (!existsSync(fullPath)) {
					throw new Error(`File not found: ${filePath}`);
				}

				const content = readFileSync(fullPath, 'utf-8');
				const ext = extname(fullPath);
				const mimeType = ext === '.ts' ? 'text/typescript' : 'text/javascript';

				return {
					contents: [
						{
							uri: uri.href,
							text: content,
							mimeType,
						},
					],
				};
			}
		);
	}

	/**
	 * 注册配置资源
	 */
	private registerConfigResources(server: McpServer): void {
		server.registerResource(
			'project-settings',
			'cocos://config/settings',
			{
				title: 'Project Settings',
				description: 'Project configuration and settings',
				mimeType: 'application/json',
			},
			async (uri) => {
				const settings = this.getProjectSettings();
				return {
					contents: [
						{
							uri: uri.href,
							text: JSON.stringify(settings, null, 2),
							mimeType: 'application/json',
						},
					],
				};
			}
		);
	}

	/**
	 * 获取项目结构
	 */
	private getProjectStructure(): any {
		const structure: any = {};
		const assetsPath = join(this.projectRoot, 'assets');

		if (existsSync(assetsPath)) {
			structure.assets = this.getDirectoryStructure(assetsPath);
		}

		const extensionsPath = join(this.projectRoot, 'extensions');
		if (existsSync(extensionsPath)) {
			structure.extensions = this.getDirectoryStructure(extensionsPath);
		}

		return structure;
	}

	/**
	 * 获取目录结构
	 */
	private getDirectoryStructure(
		dirPath: string,
		maxDepth: number = 3,
		currentDepth: number = 0
	): any {
		if (currentDepth >= maxDepth) return '...';

		try {
			const items = readdirSync(dirPath);
			const structure: any = {};

			for (const item of items) {
				if (item.startsWith('.')) continue;

				const itemPath = join(dirPath, item);
				const stat = statSync(itemPath);

				if (stat.isDirectory()) {
					structure[item] = this.getDirectoryStructure(
						itemPath,
						maxDepth,
						currentDepth + 1
					);
				} else {
					structure[item] = {
						type: 'file',
						size: stat.size,
						ext: extname(item),
					};
				}
			}

			return structure;
		} catch (error) {
			return { error: 'Access denied' };
		}
	}

	/**
	 * 获取场景列表
	 */
	private getSceneList(): any[] {
		const scenesPath = join(this.projectRoot, 'assets', 'Scene');
		const scenes: any[] = [];

		if (existsSync(scenesPath)) {
			const files = readdirSync(scenesPath);
			for (const file of files) {
				if (file.endsWith('.scene')) {
					scenes.push({
						name: file.replace('.scene', ''),
						path: relative(this.projectRoot, join(scenesPath, file)),
						fullPath: join(scenesPath, file),
					});
				}
			}
		}

		return scenes;
	}

	/**
	 * 获取场景信息
	 */
	private getSceneInfo(sceneName: string): any {
		const scenePath = join(
			this.projectRoot,
			'assets',
			'Scene',
			`${sceneName}.scene`
		);

		if (!existsSync(scenePath)) {
			throw new Error(`Scene not found: ${sceneName}`);
		}

		try {
			const sceneContent = readFileSync(scenePath, 'utf-8');
			const sceneData = JSON.parse(sceneContent);

			return {
				name: sceneName,
				path: relative(this.projectRoot, scenePath),
				data: sceneData,
				size: statSync(scenePath).size,
			};
		} catch (error) {
			return {
				name: sceneName,
				path: relative(this.projectRoot, scenePath),
				error: 'Failed to parse scene file',
			};
		}
	}

	/**
	 * 根据类型获取资源
	 */
	private getAssetsByType(assetType: string): any[] {
		const assetsPath = join(this.projectRoot, 'assets');
		const assets: any[] = [];

		const typeMapping: { [key: string]: string[] } = {
			images: ['.png', '.jpg', '.jpeg', '.gif', '.bmp'],
			audio: ['.mp3', '.wav', '.ogg', '.m4a'],
			prefabs: ['.prefab'],
			scripts: ['.ts', '.js'],
			fonts: ['.ttf', '.otf', '.fnt'],
			materials: ['.mtl'],
		};

		const extensions = typeMapping[assetType] || [];
		if (extensions.length === 0) return assets;

		this.findAssetsByExtensions(assetsPath, extensions, assets);
		return assets;
	}

	/**
	 * 根据扩展名查找资源
	 */
	private findAssetsByExtensions(
		dirPath: string,
		extensions: string[],
		assets: any[]
	): void {
		if (!existsSync(dirPath)) return;

		try {
			const items = readdirSync(dirPath);
			for (const item of items) {
				if (item.startsWith('.')) continue;

				const itemPath = join(dirPath, item);
				const stat = statSync(itemPath);

				if (stat.isDirectory()) {
					this.findAssetsByExtensions(itemPath, extensions, assets);
				} else {
					const ext = extname(item).toLowerCase();
					if (extensions.includes(ext)) {
						assets.push({
							name: item,
							path: relative(this.projectRoot, itemPath),
							size: stat.size,
							extension: ext,
						});
					}
				}
			}
		} catch (error) {
			console.error(`Error reading directory ${dirPath}:`, error);
		}
	}

	/**
	 * 获取项目设置
	 */
	private getProjectSettings(): any {
		const settingsPath = join(this.projectRoot, 'settings');
		const settings: any = {};

		if (existsSync(settingsPath)) {
			try {
				const files = readdirSync(settingsPath, { recursive: true });
				for (const file of files) {
					if (typeof file === 'string' && file.endsWith('.json')) {
						const filePath = join(settingsPath, file);
						try {
							const content = readFileSync(filePath, 'utf-8');
							const data = JSON.parse(content);
							settings[file.replace('.json', '')] = data;
						} catch (error) {
							settings[file.replace('.json', '')] = {
								error: 'Failed to parse',
							};
						}
					}
				}
			} catch (error) {
				settings.error = 'Failed to read settings directory';
			}
		}

		return settings;
	}
}

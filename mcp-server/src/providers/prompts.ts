import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { completable } from '@modelcontextprotocol/sdk/server/completable.js';
import { z } from 'zod';

/**
 * Cocos Creator提示提供者
 * 提供Cocos开发相关的提示模板，帮助AI理解开发上下文
 */
export class CocosPromptProvider {
	constructor() {}

	/**
	 * 注册所有提示到MCP服务器
	 */
	register(server: McpServer): void {
		this.registerDevelopmentPrompts(server);
		this.registerDebuggingPrompts(server);
		this.registerOptimizationPrompts(server);
		this.registerLearningPrompts(server);
	}

	/**
	 * 注册开发相关提示
	 */
	private registerDevelopmentPrompts(server: McpServer): void {
		// 组件开发提示
		server.registerPrompt(
			'develop-component',
			{
				title: 'Component Development',
				description: 'Guide for developing Cocos Creator components',
				argsSchema: {
					componentType: completable(z.string(), (value) => {
						return [
							'UI',
							'Game Logic',
							'Animation',
							'Physics',
							'Audio',
							'Network',
						].filter((type) =>
							type.toLowerCase().includes(value.toLowerCase())
						);
					}),
					functionality: z
						.string()
						.describe('Describe the component functionality'),
					complexity: completable(
						z.enum(['simple', 'medium', 'complex']),
						(value) => {
							return (['simple', 'medium', 'complex'] as const).filter(
								(level) => level.startsWith(value)
							);
						}
					),
				},
			},
			({ componentType, functionality, complexity }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I need help developing a ${componentType} component in Cocos Creator.

Functionality: ${functionality}
Complexity Level: ${complexity}

Please provide:
1. Component structure and architecture
2. Key properties and methods needed
3. Best practices for this type of component
4. Code examples with TypeScript
5. Common pitfalls to avoid
6. Performance considerations
7. Testing strategies

Consider Cocos Creator's component lifecycle, event system, and node hierarchy when providing guidance.`,
						},
					},
				],
			})
		);

		// 场景设计提示
		server.registerPrompt(
			'design-scene',
			{
				title: 'Scene Design',
				description: 'Guide for designing Cocos Creator scenes',
				argsSchema: {
					sceneType: completable(z.string(), (value) => {
						return [
							'Game Level',
							'Main Menu',
							'Settings',
							'Loading',
							'Game Over',
							'Shop',
						].filter((type) =>
							type.toLowerCase().includes(value.toLowerCase())
						);
					}),
					gameGenre: completable(z.string(), (value) => {
						return [
							'Puzzle',
							'Action',
							'RPG',
							'Strategy',
							'Casual',
							'Platformer',
						].filter((genre) =>
							genre.toLowerCase().includes(value.toLowerCase())
						);
					}),
					targetPlatform: completable(
						z.enum(['Web', 'Mobile', 'Desktop', 'All']),
						(value) => {
							return (['Web', 'Mobile', 'Desktop', 'All'] as const).filter(
								(platform) => platform.startsWith(value)
							);
						}
					),
				},
			},
			({ sceneType, gameGenre, targetPlatform }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I'm designing a ${sceneType} scene for a ${gameGenre} game targeting ${targetPlatform} platform(s).

Please provide guidance on:
1. Scene hierarchy and node organization
2. UI layout and responsive design considerations
3. Asset organization and optimization
4. Component architecture for this scene type
5. Performance optimization strategies
6. Platform-specific considerations
7. User experience best practices
8. Transition and loading strategies

Include specific recommendations for Cocos Creator's Canvas, UI system, and resource management.`,
						},
					},
				],
			})
		);

		// 游戏逻辑提示
		server.registerPrompt(
			'implement-game-logic',
			{
				title: 'Game Logic Implementation',
				description: 'Guide for implementing game logic in Cocos Creator',
				argsSchema: {
					gameFeature: z
						.string()
						.describe('Describe the game feature to implement'),
					dataManagement: completable(
						z.enum(['Local Storage', 'Server Sync', 'Memory Only', 'Hybrid']),
						(value) => {
							return (
								[
									'Local Storage',
									'Server Sync',
									'Memory Only',
									'Hybrid',
								] as const
							).filter((option) =>
								option.toLowerCase().includes(value.toLowerCase())
							);
						}
					),
					complexity: completable(
						z.enum(['simple', 'medium', 'complex']),
						(value) => {
							return (['simple', 'medium', 'complex'] as const).filter(
								(level) => level.startsWith(value)
							);
						}
					),
				},
			},
			({ gameFeature, dataManagement, complexity }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I need to implement the following game feature: ${gameFeature}

Data Management Approach: ${dataManagement}
Complexity Level: ${complexity}

Please provide:
1. System architecture and design patterns
2. Data structures and state management
3. Event handling and communication between components
4. Error handling and edge cases
5. Performance optimization techniques
6. Testing and debugging strategies
7. Code examples with TypeScript
8. Integration with Cocos Creator's systems

Consider scalability, maintainability, and Cocos Creator best practices.`,
						},
					},
				],
			})
		);
	}

	/**
	 * 注册调试相关提示
	 */
	private registerDebuggingPrompts(server: McpServer): void {
		// 性能调试提示
		server.registerPrompt(
			'debug-performance',
			{
				title: 'Performance Debugging',
				description: 'Guide for debugging performance issues in Cocos Creator',
				argsSchema: {
					issueType: completable(z.string(), (value) => {
						return [
							'Frame Rate Drop',
							'Memory Leak',
							'Loading Time',
							'Rendering Issues',
							'Audio Lag',
						].filter((issue) =>
							issue.toLowerCase().includes(value.toLowerCase())
						);
					}),
					platform: completable(
						z.enum(['Web', 'Mobile', 'Desktop']),
						(value) => {
							return (['Web', 'Mobile', 'Desktop'] as const).filter(
								(platform) => platform.startsWith(value)
							);
						}
					),
					symptoms: z.string().describe('Describe the performance symptoms'),
				},
			},
			({ issueType, platform, symptoms }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I'm experiencing performance issues in my Cocos Creator project:

Issue Type: ${issueType}
Platform: ${platform}
Symptoms: ${symptoms}

Please help me debug this issue by providing:
1. Common causes for this type of performance problem
2. Debugging tools and techniques in Cocos Creator
3. Profiling strategies and what to look for
4. Step-by-step debugging process
5. Code optimization techniques
6. Asset optimization recommendations
7. Platform-specific considerations
8. Prevention strategies for future development

Include specific Cocos Creator profiling tools and browser developer tools usage.`,
						},
					},
				],
			})
		);

		// 错误诊断提示
		server.registerPrompt(
			'diagnose-error',
			{
				title: 'Error Diagnosis',
				description: 'Guide for diagnosing and fixing errors in Cocos Creator',
				argsSchema: {
					errorMessage: z.string().describe('The error message or description'),
					context: z.string().describe('When and where the error occurs'),
					errorType: completable(z.string(), (value) => {
						return [
							'Runtime Error',
							'Compilation Error',
							'Asset Loading Error',
							'Network Error',
							'Logic Error',
						].filter((type) =>
							type.toLowerCase().includes(value.toLowerCase())
						);
					}),
				},
			},
			({ errorMessage, context, errorType }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I'm encountering an error in my Cocos Creator project:

Error Type: ${errorType}
Error Message: ${errorMessage}
Context: ${context}

Please help me diagnose and fix this error by providing:
1. Possible root causes for this error
2. Step-by-step debugging approach
3. Common solutions and workarounds
4. How to prevent similar errors in the future
5. Relevant Cocos Creator documentation or resources
6. Code examples showing correct implementation
7. Testing strategies to catch such errors early

Focus on Cocos Creator-specific solutions and best practices.`,
						},
					},
				],
			})
		);
	}

	/**
	 * 注册优化相关提示
	 */
	private registerOptimizationPrompts(server: McpServer): void {
		// 性能优化提示
		server.registerPrompt(
			'optimize-performance',
			{
				title: 'Performance Optimization',
				description: 'Guide for optimizing Cocos Creator project performance',
				argsSchema: {
					targetPlatform: completable(
						z.enum(['Web', 'Mobile', 'Desktop', 'All']),
						(value) => {
							return (['Web', 'Mobile', 'Desktop', 'All'] as const).filter(
								(platform) => platform.startsWith(value)
							);
						}
					),
					optimizationArea: completable(z.string(), (value) => {
						return [
							'Rendering',
							'Memory',
							'Loading',
							'Audio',
							'Physics',
							'Networking',
						].filter((area) =>
							area.toLowerCase().includes(value.toLowerCase())
						);
					}),
					currentIssues: z
						.string()
						.describe('Describe current performance issues'),
				},
			},
			({ targetPlatform, optimizationArea, currentIssues }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I need to optimize my Cocos Creator project performance:

Target Platform: ${targetPlatform}
Optimization Area: ${optimizationArea}
Current Issues: ${currentIssues}

Please provide optimization strategies including:
1. Platform-specific optimization techniques
2. Asset optimization best practices
3. Code optimization patterns
4. Memory management strategies
5. Rendering optimization techniques
6. Build settings and configurations
7. Profiling and measurement techniques
8. Performance budgets and targets

Focus on actionable steps and Cocos Creator-specific optimizations.`,
						},
					},
				],
			})
		);

		// 资源优化提示
		server.registerPrompt(
			'optimize-assets',
			{
				title: 'Asset Optimization',
				description: 'Guide for optimizing assets in Cocos Creator',
				argsSchema: {
					assetType: completable(z.string(), (value) => {
						return [
							'Images',
							'Audio',
							'Animations',
							'Fonts',
							'Prefabs',
							'Scripts',
						].filter((type) =>
							type.toLowerCase().includes(value.toLowerCase())
						);
					}),
					targetSize: z
						.string()
						.describe('Target file size or performance goal'),
					platform: completable(
						z.enum(['Web', 'Mobile', 'Desktop']),
						(value) => {
							return (['Web', 'Mobile', 'Desktop'] as const).filter(
								(platform) => platform.startsWith(value)
							);
						}
					),
				},
			},
			({ assetType, targetSize, platform }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I need to optimize ${assetType} assets for ${platform} platform:

Target: ${targetSize}

Please provide optimization guidance including:
1. Asset format recommendations for the platform
2. Compression and quality settings
3. Resolution and size optimization
4. Loading strategies (preload vs dynamic loading)
5. Asset bundling and organization
6. Runtime optimization techniques
7. Tools and workflows for asset optimization
8. Quality vs performance trade-offs

Include specific Cocos Creator asset pipeline recommendations.`,
						},
					},
				],
			})
		);
	}

	/**
	 * 注册学习相关提示
	 */
	private registerLearningPrompts(server: McpServer): void {
		// 学习路径提示
		server.registerPrompt(
			'learning-path',
			{
				title: 'Learning Path',
				description: 'Personalized learning path for Cocos Creator development',
				argsSchema: {
					currentLevel: completable(
						z.enum(['Beginner', 'Intermediate', 'Advanced']),
						(value) => {
							return (['Beginner', 'Intermediate', 'Advanced'] as const).filter(
								(level) => level.startsWith(value)
							);
						}
					),
					focusArea: completable(z.string(), (value) => {
						return [
							'Game Development',
							'UI Development',
							'Performance Optimization',
							'Publishing',
							'Scripting',
						].filter((area) =>
							area.toLowerCase().includes(value.toLowerCase())
						);
					}),
					timeCommitment: completable(z.string(), (value) => {
						return [
							'1-2 hours/week',
							'3-5 hours/week',
							'5-10 hours/week',
							'10+ hours/week',
						].filter((time) => time.includes(value));
					}),
					goals: z
						.string()
						.describe('Your learning goals and what you want to achieve'),
				},
			},
			({ currentLevel, focusArea, timeCommitment, goals }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I want to improve my Cocos Creator skills:

Current Level: ${currentLevel}
Focus Area: ${focusArea}
Time Commitment: ${timeCommitment}
Goals: ${goals}

Please create a personalized learning path including:
1. Recommended learning sequence and milestones
2. Essential concepts and skills to master
3. Practical projects and exercises
4. Resources (documentation, tutorials, examples)
5. Community resources and support
6. Assessment methods to track progress
7. Common challenges and how to overcome them
8. Next steps after achieving current goals

Tailor the recommendations to my current level and available time.`,
						},
					},
				],
			})
		);

		// 最佳实践提示
		server.registerPrompt(
			'best-practices',
			{
				title: 'Best Practices',
				description: 'Cocos Creator development best practices',
				argsSchema: {
					category: completable(z.string(), (value) => {
						return [
							'Project Structure',
							'Code Organization',
							'Asset Management',
							'Performance',
							'Testing',
							'Deployment',
						].filter((cat) => cat.toLowerCase().includes(value.toLowerCase()));
					}),
					projectType: completable(z.string(), (value) => {
						return [
							'Small Game',
							'Medium Game',
							'Large Game',
							'Educational App',
							'Business App',
						].filter((type) =>
							type.toLowerCase().includes(value.toLowerCase())
						);
					}),
				},
			},
			({ category, projectType }) => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: `I want to learn best practices for ${category} in Cocos Creator for a ${projectType}:

Please provide comprehensive guidance including:
1. Industry-standard practices and conventions
2. Cocos Creator-specific recommendations
3. Common anti-patterns to avoid
4. Tools and workflows that support best practices
5. Code examples demonstrating good practices
6. Scalability considerations
7. Maintenance and long-term sustainability
8. Team collaboration best practices

Focus on practical, actionable advice that I can implement immediately.`,
						},
					},
				],
			})
		);
	}
}

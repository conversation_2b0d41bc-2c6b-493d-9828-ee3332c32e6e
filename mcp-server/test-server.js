#!/usr/bin/env node

/**
 * 简单的MCP服务器测试脚本
 * 用于验证服务器是否正常工作
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 启动Cocos Creator MCP服务器测试...\n');

// 启动MCP服务器
const serverProcess = spawn('node', [join(__dirname, 'dist/index.js')], {
  stdio: ['pipe', 'pipe', 'pipe'],
  cwd: dirname(__dirname) // 项目根目录
});

let serverReady = false;

// 监听服务器输出
serverProcess.stderr.on('data', (data) => {
  const output = data.toString();
  console.log('📡 服务器输出:', output.trim());
  
  if (output.includes('Cocos MCP Server running')) {
    serverReady = true;
    console.log('✅ 服务器启动成功！\n');
    testMCPProtocol();
  }
});

serverProcess.stdout.on('data', (data) => {
  console.log('📤 服务器响应:', data.toString());
});

serverProcess.on('error', (error) => {
  console.error('❌ 服务器启动失败:', error);
  process.exit(1);
});

// 测试MCP协议
function testMCPProtocol() {
  console.log('🧪 开始测试MCP协议...\n');
  
  // 发送初始化请求
  const initRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {
        roots: {
          listChanged: true
        },
        sampling: {}
      },
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  };
  
  console.log('📨 发送初始化请求...');
  serverProcess.stdin.write(JSON.stringify(initRequest) + '\n');
  
  // 等待响应后发送资源列表请求
  setTimeout(() => {
    const resourcesRequest = {
      jsonrpc: "2.0",
      id: 2,
      method: "resources/list"
    };
    
    console.log('📨 发送资源列表请求...');
    serverProcess.stdin.write(JSON.stringify(resourcesRequest) + '\n');
  }, 1000);
  
  // 等待响应后发送工具列表请求
  setTimeout(() => {
    const toolsRequest = {
      jsonrpc: "2.0",
      id: 3,
      method: "tools/list"
    };
    
    console.log('📨 发送工具列表请求...');
    serverProcess.stdin.write(JSON.stringify(toolsRequest) + '\n');
  }, 2000);
  
  // 等待响应后发送提示列表请求
  setTimeout(() => {
    const promptsRequest = {
      jsonrpc: "2.0",
      id: 4,
      method: "prompts/list"
    };
    
    console.log('📨 发送提示列表请求...');
    serverProcess.stdin.write(JSON.stringify(promptsRequest) + '\n');
  }, 3000);
  
  // 5秒后结束测试
  setTimeout(() => {
    console.log('\n✅ 测试完成！');
    console.log('🔧 如果看到上述响应，说明MCP服务器工作正常。');
    console.log('📖 现在可以在AI客户端中配置此服务器了。');
    
    serverProcess.kill();
    process.exit(0);
  }, 5000);
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 测试被中断');
  if (serverProcess) {
    serverProcess.kill();
  }
  process.exit(0);
});

console.log('⏳ 等待服务器启动...');

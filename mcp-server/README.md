# Cocos Creator MCP Server

一个为AI大模型提供Cocos Creator项目上下文和操作能力的MCP（Model Context Protocol）服务器。

## 功能特性

### 📁 资源访问 (Resources)
- **项目信息**: 获取项目基本信息、结构和配置
- **场景管理**: 访问场景列表和详细信息
- **资源浏览**: 按类型浏览项目资源（图片、音频、脚本等）
- **代码访问**: 读取和分析TypeScript/JavaScript源代码
- **配置查看**: 访问项目设置和配置文件

### 🔧 开发工具 (Tools)
- **文件操作**: 创建、读取、修改项目文件
- **场景操作**: 创建新场景、分析场景结构
- **代码生成**: 自动生成Cocos Creator组件
- **项目分析**: 健康检查、代码分析
- **性能优化**: 项目优化建议和工具

### 💡 智能提示 (Prompts)
- **开发指导**: 组件开发、场景设计、游戏逻辑实现
- **调试帮助**: 性能调试、错误诊断
- **优化建议**: 性能优化、资源优化
- **学习路径**: 个性化学习建议、最佳实践

## 安装和使用

### 1. 安装依赖

```bash
cd mcp-server
npm install
```

### 2. 构建项目

```bash
npm run build
```

### 3. 启动服务器

#### Stdio模式（推荐用于AI客户端）
```bash
npm run start:stdio
```

#### HTTP模式（用于Web集成）
```bash
npm run start:http
```

或指定端口：
```bash
npm run start:http -- --port 3001
```

## 配置AI客户端

### Claude Desktop配置

在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "cocos-creator": {
      "command": "node",
      "args": ["/path/to/your/mcp-server/dist/index.js"],
      "cwd": "/path/to/your/cocos-project"
    }
  }
}
```

### 其他MCP客户端

对于支持MCP协议的其他AI客户端，使用以下连接信息：

- **Stdio**: 运行 `node dist/index.js --transport stdio`
- **HTTP**: 连接到 `http://localhost:3000/mcp`

## 使用示例

### 获取项目信息
```
请帮我分析当前Cocos Creator项目的结构
```

AI将使用 `cocos://project/info` 资源获取项目信息。

### 创建新组件
```
帮我创建一个名为PlayerController的游戏组件，包含移动和跳跃功能
```

AI将使用 `generate-component` 工具创建组件。

### 场景分析
```
分析Main场景的结构和组件使用情况
```

AI将使用 `analyze-scene` 工具分析场景。

### 性能优化
```
我的游戏在移动设备上运行缓慢，请帮我优化
```

AI将使用性能优化相关的提示和工具提供建议。

## 资源列表

### 项目资源
- `cocos://project/info` - 项目基本信息
- `cocos://config/settings` - 项目设置

### 场景资源
- `cocos://scenes/list` - 场景列表
- `cocos://scenes/{sceneName}` - 特定场景详情

### 资源文件
- `cocos://assets/{assetType}` - 按类型分组的资源
- `cocos://code/{filePath}` - 代码文件内容

## 工具列表

### 文件操作
- `create-file` - 创建新文件
- `read-file` - 读取文件内容
- `modify-file` - 修改文件内容

### 场景操作
- `create-scene` - 创建新场景
- `analyze-scene` - 分析场景结构

### 代码生成
- `generate-component` - 生成组件代码

### 项目分析
- `project-health-check` - 项目健康检查
- `analyze-code` - 代码分析

## 提示模板

### 开发相关
- `develop-component` - 组件开发指导
- `design-scene` - 场景设计指导
- `implement-game-logic` - 游戏逻辑实现

### 调试相关
- `debug-performance` - 性能调试
- `diagnose-error` - 错误诊断

### 优化相关
- `optimize-performance` - 性能优化
- `optimize-assets` - 资源优化

### 学习相关
- `learning-path` - 学习路径规划
- `best-practices` - 最佳实践

## 开发和扩展

### 项目结构
```
mcp-server/
├── src/
│   ├── index.ts              # 主服务器入口
│   └── providers/
│       ├── resources.ts      # 资源提供者
│       ├── tools.ts         # 工具提供者
│       └── prompts.ts       # 提示提供者
├── dist/                    # 编译输出
├── package.json
├── tsconfig.json
└── README.md
```

### 添加新功能

1. **添加新资源**: 在 `resources.ts` 中注册新的资源类型
2. **添加新工具**: 在 `tools.ts` 中实现新的操作工具
3. **添加新提示**: 在 `prompts.ts` 中创建新的提示模板

### 调试和测试

使用MCP Inspector进行调试：
```bash
npx @modelcontextprotocol/inspector node dist/index.js
```

## 注意事项

1. **权限**: 确保MCP服务器对项目目录有适当的读写权限
2. **路径**: 服务器需要在Cocos Creator项目根目录运行
3. **版本**: 需要Node.js 18.0.0或更高版本
4. **安全**: HTTP模式默认启用DNS重绑定保护

## 故障排除

### 常见问题

1. **连接失败**: 检查Node.js版本和依赖安装
2. **权限错误**: 确保对项目目录有读写权限
3. **路径问题**: 确保在正确的项目目录运行服务器

### 日志和调试

服务器日志输出到stderr，可以通过以下方式查看：
```bash
node dist/index.js 2> server.log
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

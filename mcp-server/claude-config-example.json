{
  "mcpServers": {
    "cocos-creator": {
      "command": "node",
      "args": ["/path/to/your/cocos-project/mcp-server/dist/index.js"],
      "cwd": "/path/to/your/cocos-project",
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}

// 使用说明:
// 1. 将此文件内容复制到Claude Desktop的配置文件中
// 2. 将 "/path/to/your/cocos-project" 替换为你的实际项目路径
// 3. 确保路径使用绝对路径
// 4. 重启Claude Desktop

// macOS配置文件位置:
// ~/Library/Application Support/Claude/claude_desktop_config.json

// Windows配置文件位置:
// %APPDATA%\Claude\claude_desktop_config.json

// Linux配置文件位置:
// ~/.config/Claude/claude_desktop_config.json

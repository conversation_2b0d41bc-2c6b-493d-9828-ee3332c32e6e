{"name": "cocos-mcp-server", "version": "1.0.0", "description": "MCP Server for Cocos Creator AI Integration", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/index.js", "start:stdio": "node dist/index.js --transport stdio", "start:http": "node dist/index.js --transport http --port 3000"}, "keywords": ["mcp", "cocos", "ai", "model-context-protocol"], "author": "myl", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.17.1", "zod": "^3.22.4", "express": "^4.18.2"}, "devDependencies": {"@types/node": "^18.17.1", "@types/express": "^4.17.17", "typescript": "^5.8.2"}, "engines": {"node": ">=18.0.0"}}
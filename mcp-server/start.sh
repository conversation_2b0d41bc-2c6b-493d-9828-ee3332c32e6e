#!/bin/bash

# Cocos Creator MCP Server 启动脚本

# 检查Node.js版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "错误: 需要Node.js 18.0.0或更高版本，当前版本: $(node -v)"
    exit 1
fi

# 检查是否在Cocos Creator项目目录
if [ ! -f "package.json" ]; then
    echo "错误: 请在Cocos Creator项目根目录运行此脚本"
    exit 1
fi

# 检查MCP服务器是否已构建
if [ ! -f "mcp-server/dist/index.js" ]; then
    echo "构建MCP服务器..."
    cd mcp-server
    npm run build
    cd ..
fi

# 获取传输类型参数
TRANSPORT=${1:-stdio}
PORT=${2:-3000}

echo "启动Cocos Creator MCP服务器..."
echo "传输类型: $TRANSPORT"

if [ "$TRANSPORT" = "http" ]; then
    echo "HTTP端口: $PORT"
    echo "访问地址: http://localhost:$PORT/mcp"
    cd mcp-server
    node dist/index.js --transport http --port $PORT
else
    echo "使用stdio传输（适用于AI客户端）"
    cd mcp-server
    node dist/index.js --transport stdio
fi

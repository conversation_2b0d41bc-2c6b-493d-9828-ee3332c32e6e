[{"__type__": "cc.SceneAsset", "_name": "C1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "C1", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 571}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 572}, "_id": "2e6d9d13-d514-4c50-b664-2e319d90abb9"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 409}, {"__id__": 11}, {"__id__": 155}, {"__id__": 117}, {"__id__": 508}, {"__id__": 159}, {"__id__": 414}, {"__id__": 427}], "_active": true, "_components": [{"__id__": 568}, {"__id__": 569}, {"__id__": 570}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 360, "y": 640, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "Info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -336.09000000000003, "y": 488.95100000000025, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7aNOGxgoJO76HH1Ol2NNOB"}, {"__type__": "243223R+y9C8LfOKZXSm/q1", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "Name_Label": {"__id__": 7}, "Time_Label": {"__id__": 114}, "User_Avatar": {"__id__": 111}, "Parent_Name": {"__id__": 63}, "Parent_Avatar": null, "Ap_Label": {"__id__": 40}, "Mana_Label": {"__id__": 16}, "Points_Label": {"__id__": 28}, "Game_Start_Node": {"__id__": 118}, "Game_Pause_Node": {"__id__": 116}, "Game_Label": {"__id__": 143}, "Panda_Spine": {"__id__": 154}, "TiLi_Node": {"__id__": 159}, "GB_Node": {"__id__": 409}, "Music_Node": {"__id__": 94}, "D_Number_Label": {"__id__": 384}, "Message_Label": {"__id__": 404}, "Message_Node": {"__id__": 401}, "Fragment_Tips": {"__id__": 414}, "fragmentAmountLabel": {"__id__": 423}, "MyLog_Node": {"__id__": 427}, "_id": "6f8JSQrZhAeZhT0T2/Aa+B"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "5e7cJD2MdLEbBzVIlx5h3O"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 112}, {"__id__": 7}, {"__id__": 113}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 1.342, "y": -55.521, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "afYz+JJtBJ9q9TMSbGC0j6"}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 8}], "_active": true, "_components": [{"__id__": 110}, {"__id__": 111}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 6.451, "y": 9.939, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2fKOW9y3RGc7UEP8tdWSvO"}, {"__type__": "cc.Node", "_name": "name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 106}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 109}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -290.675, "y": 21.878, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "902K1PpQdB/a+kOqEHcbiC"}, {"__type__": "cc.Node", "_name": "Top", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 12}, {"__id__": 24}, {"__id__": 36}, {"__id__": 53}, {"__id__": 67}, {"__id__": 72}, {"__id__": 77}, {"__id__": 84}, {"__id__": 89}, {"__id__": 94}], "_active": true, "_components": [{"__id__": 104}, {"__id__": 105}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.311, "y": 488.477, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "26csoDEyFLDokLaSI8W7zn"}, {"__type__": "cc.Node", "_name": "Fragment", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 13}, {"__id__": 20}], "_active": true, "_components": [{"__id__": 23}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -115.04000000000002, "y": 27.87000000000012, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b8T639J+1I5rGziKPEmmym"}, {"__type__": "cc.Node", "_name": "bg1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 14}], "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 13.8, "y": -0.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "63DwN+IVRIiLdRAS5py4ad"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -45.587501525878906, "y": 0.589, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fe2iRFsDVB/KJolbiPG02h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 21.824996948242188, "height": 35.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c3ikSV21RCOr8DP9t6fBit"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "e5+1h4ZdNIyrKoS/iBD5GT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 173, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "59DwaNhzNDGbsTRXHf3vh5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "405c0df7-9e3e-44b7-8735-41906d933e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e4nJhmiONLh4OB24oy3nBm"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 30, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "e2sQsOl7tNa7Lnwz/++T+t"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -77.067, "y": 6.36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "26SJCFL4BCSLu/2yK/WoRU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4cLMAajYlFTqP6R6LW11/E"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c1a0c45a-6134-424c-a54b-807087a14929@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d8wHmxS9JNm6tihxN7Mc9l"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cUqm0j+BK7qWus7noea4c"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 25}, {"__id__": 32}], "_active": true, "_components": [{"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 122.781, "y": 27.762, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "233EySFMVOOJcVjQuCE+5s"}, {"__type__": "cc.Node", "_name": "bg1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [{"__id__": 26}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -5.85, "y": 2.633, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "baJsMYIO5BvYTQ39/vxFL0"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [{"__id__": 27}, {"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -45.587501525878906, "y": 0.625, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1d52PL9AhMcqbOS4E8Fldc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 21.824996948242188, "height": 35.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dbSjWCr5ZEDrjc8SbIhQFi"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d35fv8+PBNLKG/gj96OlHZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 173, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d7xHlFGhhNOKHqBR038W43"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "405c0df7-9e3e-44b7-8735-41906d933e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "22nTsspoNMZKKpMA87uvqY"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 30, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "8avtHy0DxDjqb2PFWKjlu5"}, {"__type__": "cc.Node", "_name": "bg5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 33}, {"__id__": 34}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -97.416, "y": 2.245, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48lN/ANz5LJYKVG6R9VNd7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c1W5AWA61C4KIm6QpYte1p"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "256c9c82-847d-4176-b657-cf3ffc83f273@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d67pcFiutBFp1v78RizlhM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fbDUdjBTxDY6oWFUD5lVJF"}, {"__type__": "cc.Node", "_name": "Energy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 37}, {"__id__": 44}, {"__id__": 47}], "_active": true, "_components": [{"__id__": 50}, {"__id__": 51}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -111.971, "y": -27.835, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85jnlO4ZRA1o+ftkOaZ8K+"}, {"__type__": "cc.Node", "_name": "bg1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [{"__id__": 38}], "_active": true, "_components": [{"__id__": 41}, {"__id__": 42}, {"__id__": 43}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 3.284, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2fzWD/Ca9IaotOUqm9uW1i"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 37}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -37.93750152587891, "y": 1.1510000000000673, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c7yV4YkexMeYpn0A6IQBrk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 21.824996948242188, "height": 35.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5eR0pbve1Mx5GzdSw0K/8f"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "fcIQMFTz1C+pQjMg8/EBDC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 185.9, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ecM8uublVJvYHypnpfHhg0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "405c0df7-9e3e-44b7-8735-41906d933e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9fYuwxf79K7L4O01UIjWoF"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 44.1, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "7diNoRKMdB5rUbLCOGoMie"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 45}, {"__id__": 46}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -82.55, "y": -1.581, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "66MWdZ4PJOvI6EDmmildlg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8bbFPjfHlHrYF4YMlxynGN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59b03709-e5ae-4e84-8fec-3d1d7517df6c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "5bxzxmJYxCdprDwdayweKn"}, {"__type__": "cc.Node", "_name": "bg2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 48}, {"__id__": 49}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 76.615, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4bqe5E4C9CEKYC3Bety6co"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5dyHAD2axC0IvPLv/OOqL3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "796bf7af-f371-4c22-b9fc-49bf8f7e0e44@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cdKfe4kTVIjofgj/sNtg7e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f4XWKYTeFMZZ1LPNbdZGW7"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 52}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "ec8/zbE1xK/5XUcHxtmgy9"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "showTiLiNode", "customEventData": ""}, {"__type__": "cc.Node", "_name": "Yqr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 54}, {"__id__": 57}, {"__id__": 60}], "_active": false, "_components": [{"__id__": 66}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -70.197, "y": -37.91399999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c7Pmin4GlCmbhCt0XtHEhX"}, {"__type__": "cc.Node", "_name": "bg7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 55}, {"__id__": 56}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "81YC3krjZGyopp9dgrfSrH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1dU3O6F51O9JLxZdRyydbt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b3f3cf9f-3bc6-4345-bc78-a2071a1402c1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2e18VU89dJXJK4fGewecKQ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -82.581, "y": 1.231, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b0xrc1X2tAOIDLfUJRwap9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 109.95999145507812, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "71FPH28OtFUL5t52Fvkt1e"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 157}, "_string": "我的邀请人:", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "c0M+6sE51C7oXkzumY8RFu"}, {"__type__": "cc.Node", "_name": "bg8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 61}], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 120.08, "y": 0.41, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2e8nLznKVBu4Ra4AhQEXER"}, {"__type__": "cc.Node", "_name": "Label-name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 63}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -81.262, "y": -0.18, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "edSM+Dp4xI84DreLItqvgs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4aGdN7FGFLlqzJQ7aJUeBE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "黎小寒", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "JiangChengYuanTi-700W", "_lineHeight": 20, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "34ZdPHeAZHl6BFKD4O5JJA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 19}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "66Z8rdWHNFy5rQ/feII1/o"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cc3d6efe-1f61-48da-acab-97bcddec9282@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4d1Zps9u9LDrewIXTiOEy5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1jyKTsk9GOYqtCpuDJ/Fw"}, {"__type__": "cc.Node", "_name": "Btn1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 68}], "_active": true, "_components": [{"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 23.286, "y": -35.349, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cbOZPBk2lCvom5MpTjYqrn"}, {"__type__": "cc.Node", "_name": "bt1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 70}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d72kgxa69GZ5kKShzZcqDy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a6wRqZngFP2KNpX4kHrdAY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "58ab0abd-9e11-492f-92f1-3a38ad9311f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7dkHt5kFtO6IHCkxpISzGu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "51EscKvuNOJpt6bRlykcRi"}, {"__type__": "cc.Node", "_name": "Btn2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 73}], "_active": true, "_components": [{"__id__": 76}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 120.826, "y": -36.744, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "37B/9cscNIZJOvquh7WDQW"}, {"__type__": "cc.Node", "_name": "bt2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -18.112, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fc9RyVo5lN1LK3HUz1AwNY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 47, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8fj52wnhhNDZgsxdjuCOVP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "971b48e2-fbd3-48cb-a403-f5b16ecada03@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c34qAxR3JKV5cut9jHZ7xt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "33q9Y4ebxEbLFMIGyi7nev"}, {"__type__": "cc.Node", "_name": "Btn3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 78}], "_active": true, "_components": [{"__id__": 81}, {"__id__": 82}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 180.355, "y": -33.874, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebzPSjO/tLIYsQF9ycaCI2"}, {"__type__": "cc.Node", "_name": "bt3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 79}, {"__id__": 80}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.379, "y": -2.795, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "25PEenPPdDjbbkwWnLFe1c"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 49, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43fGJCkpxIHpqM4KN2ri+z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "66cfa7e4-013e-430b-b704-1792d4c0f13b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bc1s9e9M9ObYOAkzYzHvj0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "95rSarP9VJXLyu3WD5VCKH"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 83}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.2, "_zoomScale": 0.8, "_target": null, "_id": "e9Vyo/witH/a4CyoW9m9/V"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "ShowLog", "customEventData": ""}, {"__type__": "cc.Node", "_name": "Btn4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 85}], "_active": true, "_components": [{"__id__": 88}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 279.2, "y": -97.954, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d5eiw5OkRA8rvHFADlBB4P"}, {"__type__": "cc.Node", "_name": "bt4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 84}, "_children": [], "_active": true, "_components": [{"__id__": 86}, {"__id__": 87}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 7.27, "y": -27.892, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a7upUf7RFDULAWVHfyEG9m"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 77}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "59IFiiYt9HZqLzHl48rKkN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "90de3e63-ea26-4537-9fc4-21c29a59653a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "92rGo23c1KxK1W8j/SANOb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d13vnQSnpOkIRo+6oXLuMj"}, {"__type__": "cc.Node", "_name": "Btn5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 90}], "_active": true, "_components": [{"__id__": 93}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 282.571, "y": -233.113, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e3t4rVNnlDpKV2EToce3VF"}, {"__type__": "cc.Node", "_name": "bt5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 89}, "_children": [], "_active": true, "_components": [{"__id__": 91}, {"__id__": 92}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "03UAnke0xEOpMYSel8T1m1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 92, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fej6/YvmlF6ZjnO9Zf9lOw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7526ac9b-172c-4716-8719-b4fd4cd07409@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "76+Re8tOZDaaa/Qxo/HEY9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "042FXb69lMmrXH6g/AfmQn"}, {"__type__": "cc.Node", "_name": "Btn6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 95}, {"__id__": 98}], "_active": true, "_components": [{"__id__": 101}, {"__id__": 102}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -292.889, "y": -118.704, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eaLh4oIulOaLhlO2vh1QjB"}, {"__type__": "cc.Node", "_name": "bg20", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a9tLyZvnBF8IZInSa6TvoE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "041Ii3cyRO24IwWx9V7gUj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5f7e915a-5fad-442b-ae6d-4d9b6690616d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d3H/p7DiBM5YH9oPxWpqE9"}, {"__type__": "cc.Node", "_name": "bg19", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": false, "_components": [{"__id__": 99}, {"__id__": 100}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "43xtQHlFpNT6sB66UMaVGV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eaJMVUxP1H872NhRnJ+rf8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3fb869fe-b25d-4016-9b32-f54f1f6f7a04@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d0ng13mOhMablVkOBgMG8t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "685VTXzeZHga0rZq/NR+cQ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 103}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.2, "_zoomScale": 0.8, "_target": null, "_id": "45RHn/dTBN4aawM64OQTbs"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "toggleBackgroundMusic", "customEventData": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 180}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f4Hgsno7dGA5SkTuDkP3GR"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 61.523000000000025, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "84M27asZtH4r3XMQYgQYaI"}, {"__type__": "cc.Node", "_name": "Mask group", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 8.101, "y": -18.553, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a5655hwJVE5JWwUzsokf78"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 109}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bb997Q1h5CjLUFzFG3i/x1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f1681ee1-8708-445a-92e5-5e3454a37f77@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "74Ma7bF0BM9ofaGKUX5WxN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e9DhUM+OBChYwpukwvooeR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "35jwtICX5G6bzgyisejLxU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b0e69e04-335b-4d45-8494-c2755b1639d7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "52xavu7nZIUrawyJUPKciJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "64JBPO6qhGBoKawuP0Vnq4"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_id": "7fxmMoH+pEl73KxFfOEJaZ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "24 : 30 : 12", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 23, "_fontSize": 23, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "dczYmkhyNGDpnwg0m6yZ6j"}, {"__type__": "cc.Node", "_name": "Time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 153}, {"__id__": 114}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 17.006, "y": -23.125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "54oAp61xVDz4IqYTHdhmQb"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 117}, "_children": [{"__id__": 134}, {"__id__": 115}], "_active": false, "_components": [{"__id__": 149}, {"__id__": 150}, {"__id__": 152}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7axxBqp9lBtYdTz1zaHvcU"}, {"__type__": "cc.Node", "_name": "Play", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 116}, {"__id__": 118}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 133}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -11.018, "y": -413.199, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e8lg2kOPZJ4J1a1FctopLd"}, {"__type__": "cc.Node", "_name": "Start", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 117}, "_children": [{"__id__": 119}], "_active": true, "_components": [{"__id__": 131}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a5VBcNGddKS6uat7P3edqj"}, {"__type__": "cc.Node", "_name": "start", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 118}, "_children": [{"__id__": 120}, {"__id__": 124}], "_active": true, "_components": [{"__id__": 127}, {"__id__": 128}, {"__id__": 129}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "43rq4/Wm5ITJa4kJcu4FOc"}, {"__type__": "cc.Node", "_name": "bg16", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 121}, {"__id__": 122}, {"__id__": 123}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -47.384, "y": 4.529, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "415AFLn45LBrHvVDZDFUKA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "33y2oixlFC7IGeZy0ZnEIU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e5aa7597-89d1-40df-8d14-335aedcc00fe@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "905WIfJ5xMeLelDdBdzPNh"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": null, "playOnLoad": true, "_clips": [{"__uuid__": "*************-48d0-a51c-8564cfaf8f16", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "*************-48d0-a51c-8564cfaf8f16", "__expectedType__": "cc.AnimationClip"}, "_id": "970PixolpAPZSwTeZkrI0u"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 125}, {"__id__": 126}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 37.953, "y": 4.217, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "42ca9XwM5FwY9RLfyxdjAL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1bJo6nmxhFCbFuq4CVxdiK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "开启", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -6}, "_shadowBlur": 2, "_id": "74InDsAytH26ADDtd2CCMm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b3ezfpzsRI4IFYblMP2tPY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7636781c-e8fa-47da-a714-03ebb243130b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cbm5/1qpxI/5SIGruZ5eRp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 130}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "63PK+xpc9Ld4rYmWUQtPO0"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "onStartGame", "customEventData": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eeSfH18U1EQoIa+QrROOzz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "01tnfznYxLVaRLL12Z0Omz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 176.801, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "7d1FmrlFZDrJHHEzdVRzAg"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [{"__id__": 135}, {"__id__": 138}, {"__id__": 141}, {"__id__": 144}], "_active": true, "_components": [{"__id__": 147}, {"__id__": 148}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4aKIpAZ/lJirPZ1shTLU4d"}, {"__type__": "cc.Node", "_name": "bg14", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 136}, {"__id__": 137}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -22.145, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97aULqVepEspSYXoPvybKU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 218, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "feaeZ2/69GcrVOqBpLnqIU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f9d4be54-ff48-4196-a496-4f7f68e39202@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "817arSvMNEmbcfvq3cpEhy"}, {"__type__": "cc.Node", "_name": "bg10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 139}, {"__id__": 140}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -70.674, "y": -23.087, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2cN2ShoVJGJ5lIGGvKfEy2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "efoWbs26RFObnioullokYp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c8259dd9-a07d-4695-a5e2-0df5c16e3f01@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "5airDbVRdMU6ePKZiowEmF"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 142}, {"__id__": 143}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 29.519, "y": 25.127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31Ilarq61AgazbGVR/G57j"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 51.88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "50YFV7jg1HSqXLwCDewODO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "暂停", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 38, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -8}, "_shadowBlur": 2, "_id": "51c5DnIEJGYIvfV/+rFeU6"}, {"__type__": "cc.Node", "_name": "bg15", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 145}, {"__id__": 146}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -44.701, "y": 25.302, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "857yrryl1EApwhe9SgzhIo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "74lOY73KNHPLXXxaFkusFu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49a023e9-fe75-452c-9224-f75ab0f085c3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2fNRqYwI1HAYOXz5RECS2q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f9qVaW9vtIeKshVOYEBWwf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "19ab7619-3f31-4732-966c-5ba3ebbd0ef7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "78cT30eTNE2aDEqSifoa1G"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "11qqOT09pPoqSNvNnwMA1o"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 151}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "c7LQcutupEK6ViGwhVa3pI"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "onPauseGame", "customEventData": ""}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "c83c2386-e884-4680-a624-33a8a3a4b57a", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "1c0ymlTZ1C26g0fBPhSJID"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 124.24391174316406, "height": 32.980000000000004}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "44x2GOBoFBhp7ZeMG+onqq"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "a7c0e0f9-f702-461d-8f20-a93814970b3d", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<PERSON><PERSON><PERSON><PERSON>", "_premultipliedAlpha": true, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": "8anmN8yd5KdZC2QAMC+r+g"}, {"__type__": "cc.Node", "_name": "Panda", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 156}, {"__id__": 157}, {"__id__": 154}, {"__id__": 158}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 27.75, "y": -220.83500000000004, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "42UMlOhQ1AbpDoxWo8sya3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 616, "height": 568}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.49116882720550936, "y": 0.18042254112136197}, "_id": "f4tBYyllJNGrYb5bV3sK/r"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 316.68499664306637, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "69bWduF2lKTZ+ifel+XBaE"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "3a9a6e62-0c1c-4556-9bab-8d44ba1dec61", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 0.6, "_id": "2d5S7P3+lHopLPZcHkaLJl"}, {"__type__": "cc.Node", "_name": "TiLi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 160}, {"__id__": 164}, {"__id__": 393}, {"__id__": 401}], "_active": false, "_components": [{"__id__": 407}, {"__id__": 408}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e45XeOSxdGHZhWRFQ99yAi"}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 161}, {"__id__": 162}, {"__id__": 163}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33m2wDcZ1EU5ZLmGVEZTSh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5a/bZe7ghH/YqbRr79ET1m"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 201}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "adhE2D6DpEmI1paWPBffa9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "a97njdCkVD75XElxOHfmah"}, {"__type__": "cc.Node", "_name": "box", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [{"__id__": 165}, {"__id__": 169}, {"__id__": 180}, {"__id__": 372}], "_active": true, "_components": [{"__id__": 391}, {"__id__": 392}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "da2cF6u9dHbLlzcR+IFusT"}, {"__type__": "cc.Node", "_name": "bg1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 166}, {"__id__": 167}, {"__id__": 168}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -84.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "50I86D07VDz6jUrufYoIzE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 588, "height": 831}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "71vjFoVC9AfLFGpUmlGSpu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a4d4d04-82c9-47e5-87e0-04d506829b42@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7a7qOou6BFVJwcZuaFtNg8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "a5pJ0q1CJAgpDnd9LWQTD+"}, {"__type__": "cc.Node", "_name": "bg2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [{"__id__": 170}, {"__id__": 175}], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 416.609, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "087YqLvoJJRLlJVsZPsWFT"}, {"__type__": "cc.Node", "_name": "bg7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 171}, {"__id__": 172}, {"__id__": 173}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 270.135, "y": -46.074, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "98H6JvzMhP7Yfo4CVD0GTw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eemLmIZW9JmL+8nzPaoAnB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b3c6a3f7-7e60-42e8-b80a-15dd4e529874@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "61QMPd5W5IM5WURDU9Qwta"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 174}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "a5rM+CeERFvYXSlng+4KZm"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "hideTiLiNode", "customEventData": ""}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 176}, {"__id__": 177}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -89.795, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "df/8x0w6pC3IyLn/7vN6W0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 152, "height": 53.36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "45h2OEah5MgIobgzEPGPY/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "体力兑换", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 36, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -8}, "_shadowBlur": 2, "_id": "a5EZePKsdLm73mBghxKMI7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 605, "height": 265}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6b1IkAdexBu7/uZEZVCIFZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "65ab5721-83c6-4fde-ae8a-be9016e109f7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e3dXl+K8BDpL7uPZlFRvzq"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [{"__id__": 181}, {"__id__": 215}, {"__id__": 246}, {"__id__": 277}, {"__id__": 308}, {"__id__": 339}], "_active": true, "_components": [{"__id__": 370}, {"__id__": 371}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -44.743, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "56ynZuNvtIALLpbyH5dGFz"}, {"__type__": "cc.Node", "_name": "item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 180}, "_children": [{"__id__": 182}, {"__id__": 185}, {"__id__": 188}, {"__id__": 191}, {"__id__": 194}, {"__id__": 197}, {"__id__": 200}], "_active": true, "_components": [{"__id__": 214}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -188.5, "y": 189.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9eC7lb6DpA14EsBS0fovP5"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [], "_active": false, "_components": [{"__id__": 183}, {"__id__": 184}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6eUXOKLqNIzYHLJ4a1fkSY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 234}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "90p5DzEgtLqrrGPfMjG2PV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73a2236a-605a-4770-aa45-edafb7ff4dd3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a01jk3nQVEuIaodyIz2c2A"}, {"__type__": "cc.Node", "_name": "bg3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [], "_active": true, "_components": [{"__id__": 186}, {"__id__": 187}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e9n08VUwdNiJXvk1Ao5xd5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 185}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "41X19zV9dJt7TSQtB/leL8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 185}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60ca564f-ddb4-4b91-a2ea-ccd9735ffb20@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "99rAiCihROXouXiZUM/9K0"}, {"__type__": "cc.Node", "_name": "T-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [], "_active": true, "_components": [{"__id__": 189}, {"__id__": 190}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 64.201, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a18kjDfRxDTJ5c5bR7agZe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 56.27598571777344, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7dyrOVSmhPlbPQkuTwXeYo"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x10", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "4aWjfBB2hBkaFLCtxbciHd"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [], "_active": true, "_components": [{"__id__": 192}, {"__id__": 193}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "45BxTFtchF/ZQbR5XTB/Rb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eaoSeLWWpM5aZuW9tWwgpd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "44b9e19f-b2c6-4c2e-91b9-2dfaf1434ef1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "cfeQDVsRRJGpHBLCvpbYLh"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [], "_active": true, "_components": [{"__id__": 195}, {"__id__": 196}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -20.372, "y": -77.267, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bbHWnH88VFbK1m9amw1zVU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "84WlOvJ7hAhomQ5B9p7R78"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ab1fddc-3b5f-4920-9c37-bdf1ae4c2ec5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "27Zd0KLBdD0YIX1JOyvQAR"}, {"__type__": "cc.Node", "_name": "M-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [], "_active": true, "_components": [{"__id__": 198}, {"__id__": 199}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 18.4, "y": -78.581, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c80WMZHApHWZDBh7xuHVZG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 39.56298828125, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "28LfQ+r0VGmpegy0hTpydm"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "10", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "bcQMq/J0dFNKI8HhB4ip9m"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [{"__id__": 201}, {"__id__": 204}, {"__id__": 207}, {"__id__": 210}], "_active": false, "_components": [{"__id__": 213}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 40.955, "y": 107.127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "41IvnFfxBEtpBl2xhjW80V"}, {"__type__": "cc.Node", "_name": "bg15", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 203}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48dHBSHRJFzJ2aOmLUkcZo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d2YhlhpXNAfrQGrppZFPmm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ef15fdd-10ee-4555-b7b6-d1ef48d39ddb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ees63yAINLHITtK1AJiyRg"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 205}, {"__id__": 206}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -16.867, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b6wkDKOVVMaLu/DGjHuhnC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "76Eth4u/dCOrmZpdZGEFE4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "13yYWS4lVNkZT8FVEUmbGJ"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 208}, {"__id__": 209}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 7.875, "y": 0.885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bcAr8UehNHk4cbvTIOkMcq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a1dLtQlA9Fkp/idPLatTTY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "44b9e19f-b2c6-4c2e-91b9-2dfaf1434ef1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "70Xfr3NFlFup/Ye0eFAqQ4"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 211}, {"__id__": 212}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 25.957, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4ebxdcKW9LkJ/752Xq9ZIv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 13.57598876953125, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d9X2vqniNFupxw+L+Mqt4K"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "bb9X5cBIJCDI4ngVAHiSs+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0fOV/loyxHg5mLDzSZBIs0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d8JXYvbj9EcIPk/x+pvvAX"}, {"__type__": "cc.Node", "_name": "item2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 180}, "_children": [{"__id__": 216}, {"__id__": 219}, {"__id__": 222}, {"__id__": 225}, {"__id__": 228}, {"__id__": 231}], "_active": true, "_components": [{"__id__": 245}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -1.5, "y": 189.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "16essNnNtDhrHmUzPomMh7"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 218}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e95AEZ46tNTZeen7yGsthb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ac7nIGj/lI96UbTMdftcfb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73a2236a-605a-4770-aa45-edafb7ff4dd3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "03h0A7P2FP/LxTpOgYxwci"}, {"__type__": "cc.Node", "_name": "T-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 64.201, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4agQqUizRDPbpm75lzYowc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 75.5269775390625, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "41VwtOrU1PGIuMn5pW8R/q"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x100", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "17f595E7NHxKoDTdGp1WhC"}, {"__type__": "cc.Node", "_name": "bg8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 223}, {"__id__": 224}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13FSsmFDtKV4Dp86WCW6dX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ddJ+WdcR5IPJrYLpjLQcwp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b5b6dec9-04e8-40aa-abeb-dc64911ad53f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "9drwRqghlGOp9KBmO+OVk3"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -26.788, "y": -78.933, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "902Z8Vs8RN06DqLC+6ecjQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6d8aCcPQRHAo0IhHalqLJx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ab1fddc-3b5f-4920-9c37-bdf1ae4c2ec5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "54WRRIdAFEHbkEmpjWswle"}, {"__type__": "cc.Node", "_name": "M-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 229}, {"__id__": 230}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 21.15, "y": -81.331, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "895G8rbdhC57YDewPpRjAw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 228}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 58.81398010253906, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "74RDcuHgVBmbM8SYHBBpQI"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 228}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "100", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "5fo57fVj1KHaybttvtbz1+"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 215}, "_children": [{"__id__": 232}, {"__id__": 235}, {"__id__": 238}, {"__id__": 241}], "_active": false, "_components": [{"__id__": 244}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 40.993, "y": 107.127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3cO9nzZNNJDLal6GqQU6BJ"}, {"__type__": "cc.Node", "_name": "bg15", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 233}, {"__id__": 234}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a7OCVFEaVK9YdvWkfpAeYs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e5AeUdlXVJWZaHGsVYh5QY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ef15fdd-10ee-4555-b7b6-d1ef48d39ddb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "41bcOUsUdI15F06ZUSUaA9"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 236}, {"__id__": 237}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -16.867, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "364b66BGZAn7lfPEDS6oNB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7b0C61+3VEZLrf6hxumV+Z"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "46eRdsZ1tMmbzKEmycxm2W"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 239}, {"__id__": 240}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 7.875, "y": 0.885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e5/yA5kqhIEIa/nad6/6Zf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 238}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9bw7GPqARKsZ5954gJuNUY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 238}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "44b9e19f-b2c6-4c2e-91b9-2dfaf1434ef1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e1ye7PRPJBJ6Dzx9iPGwuy"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 242}, {"__id__": 243}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 25.957, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ec0bLCK0NGZ7ra8ERup1f9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 13.57598876953125, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b6IE+sbT9CxbdszhbJZzDs"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "91sG32zndDJpjc/F+vdlHU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ad9eaqfP5GSrWqHF8IYjn+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f6DF+bw2tLDZDgQe7RPpZY"}, {"__type__": "cc.Node", "_name": "item3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 180}, "_children": [{"__id__": 247}, {"__id__": 250}, {"__id__": 253}, {"__id__": 256}, {"__id__": 259}, {"__id__": 262}], "_active": true, "_components": [{"__id__": 276}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 185.5, "y": 189.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c5bokO975NAJpGTWyCMgK4"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 248}, {"__id__": 249}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8cQYc7ZOtA1b9NyUXZ628j"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "beyFZhAx1FDb4H3MOjFQnm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73a2236a-605a-4770-aa45-edafb7ff4dd3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d0Q+U2UaJFX5fb0ZQLqwAv"}, {"__type__": "cc.Node", "_name": "T-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 251}, {"__id__": 252}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 64.201, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "acqDb3aIhIK6H8mS88mjcf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 79.73896789550781, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "333oRkFt5ASKw1EiAvLVcE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x240", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "e3PsGjUBpHUou1YwuhJjDJ"}, {"__type__": "cc.Node", "_name": "bg9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 254}, {"__id__": 255}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "834NNrIBlAvKdNtLRj13er"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 253}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "355pzY+Q5MiKw/qCsDpVwn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 253}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fa981ee4-7963-400b-bc09-ac237faa2637@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "28YNCsXw9HwISgH+1eo6X6"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 257}, {"__id__": 258}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -27.704, "y": -77.767, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3aLVgXJ7tNXL2ObOscYVj/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e8ubRiL2pGuIcJLUJ22/C6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ab1fddc-3b5f-4920-9c37-bdf1ae4c2ec5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "52VHxf9rVJM5mhcXTDm8Dn"}, {"__type__": "cc.Node", "_name": "M-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 260}, {"__id__": 261}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 23.899, "y": -78.164, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3cQ1aJznhIhZsvJGvcUYmo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 259}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 63.025970458984375, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "76laU0gO1HsYA1XcMT7OBj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 259}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "240", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "f1lWmLoPlORpzz30TxupW+"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 246}, "_children": [{"__id__": 263}, {"__id__": 266}, {"__id__": 269}, {"__id__": 272}], "_active": false, "_components": [{"__id__": 275}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 40.925, "y": 107.127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "67Pp8Vha1OGa7aSfP9kSOZ"}, {"__type__": "cc.Node", "_name": "bg15", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": true, "_components": [{"__id__": 264}, {"__id__": 265}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f1iYmktPBI5JGpCdwdVTGN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "97sWaQVWVNdqKVSB84Knhg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ef15fdd-10ee-4555-b7b6-d1ef48d39ddb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e8dlSLIZtKZqUo+JGmRNw1"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": true, "_components": [{"__id__": 267}, {"__id__": 268}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -16.867, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a2kaFQFpRPGZoBJZYZLLlk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 266}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "75kJCKo0xJ7rPMPRcK9FsR"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 266}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "a71iaK4q5OUJh3E4nPSevT"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": true, "_components": [{"__id__": 270}, {"__id__": 271}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 7.875, "y": 0.885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b3FOlrQOZNE5PrsvIN4m6H"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ebJUKdl+hJ0JMEhGIz0aCu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "44b9e19f-b2c6-4c2e-91b9-2dfaf1434ef1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "fdpsjK2KNPp7U4j24h7qFr"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": true, "_components": [{"__id__": 273}, {"__id__": 274}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 25.957, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fcJcvFSxJDzag6KwxmvKvq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 272}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 13.57598876953125, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9fZlaTn+pHVL0SJPdFJv+f"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 272}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "f9N3mPhNZGPY462DF2xAJd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "06UHcscCZJjKSDODjqeOyX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 246}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2ddPMPmsVGQKsf2q7or6EH"}, {"__type__": "cc.Node", "_name": "item4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 180}, "_children": [{"__id__": 278}, {"__id__": 281}, {"__id__": 284}, {"__id__": 287}, {"__id__": 290}, {"__id__": 293}], "_active": true, "_components": [{"__id__": 307}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -188.5, "y": -55.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "154azGygtH9Ji51VBQi/xx"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 277}, "_children": [], "_active": true, "_components": [{"__id__": 279}, {"__id__": 280}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "360ktzpW1G8bQfIgyBFCUf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 278}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a24X31HtdAl73CmKIoR0f3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 278}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73a2236a-605a-4770-aa45-edafb7ff4dd3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "29uAgKcuVLzYs14LzGD8Pg"}, {"__type__": "cc.Node", "_name": "T-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 277}, "_children": [], "_active": true, "_components": [{"__id__": 282}, {"__id__": 283}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 64.201, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48ABre6qJDN5SqdEZG/n38"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81.06198120117188, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "25HMa/80tGH47ZEiq06Zxg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x480", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "9du/M3zXhASag/K2pxxAla"}, {"__type__": "cc.Node", "_name": "bg10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 277}, "_children": [], "_active": true, "_components": [{"__id__": 285}, {"__id__": 286}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a4W9yKSM1Ge7r0xl25SK8Y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "01zYtAcTVKYbglhVaFmlwg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "03a5987d-e1d1-40c0-bf1c-805caf0d6073@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "31VvheJ1dP0J/V/R7vpd28"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 277}, "_children": [], "_active": true, "_components": [{"__id__": 288}, {"__id__": 289}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -35.372, "y": -80.601, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7avgegre9KRIjPsDyWqUa/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 287}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "411Mdkx3dNPaVUiHip8vN/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 287}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ab1fddc-3b5f-4920-9c37-bdf1ae4c2ec5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "924zgkfjlH4rPkIggWK7i2"}, {"__type__": "cc.Node", "_name": "M-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 277}, "_children": [], "_active": true, "_components": [{"__id__": 291}, {"__id__": 292}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 19.4, "y": -82.998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e3Paldw41P3KM1MpdqoTJ5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 290}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64.34898376464844, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cdxnQoesBLoJR303FL6Db0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 290}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "480", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "03YGGhxGZP+I0G78JMfWIu"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 277}, "_children": [{"__id__": 294}, {"__id__": 297}, {"__id__": 300}, {"__id__": 303}], "_active": false, "_components": [{"__id__": 306}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 40.949, "y": 111.539, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "81iyb51T9FPp9uZcsFlA9m"}, {"__type__": "cc.Node", "_name": "bg15", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 293}, "_children": [], "_active": true, "_components": [{"__id__": 295}, {"__id__": 296}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5c8XGcvDxDpr/iHAWRf9xh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 294}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bfPpHwmvhAHLEHoBS2IUTU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 294}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ef15fdd-10ee-4555-b7b6-d1ef48d39ddb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b5lIeo3XlB+rbIeHdj+4Sm"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 293}, "_children": [], "_active": true, "_components": [{"__id__": 298}, {"__id__": 299}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -16.867, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0d5V5hYz9Oh6aEOjQ5kgb2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cfdeIcZ2pPoI18VWtcmPvW"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "6fP4kOQPxN6a7Nd40auqTR"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 293}, "_children": [], "_active": true, "_components": [{"__id__": 301}, {"__id__": 302}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 7.875, "y": 0.885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1WR5izVRPCZbgBUt368jd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 300}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9fsgNP7ydFXaYhR3ig8c52"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 300}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "44b9e19f-b2c6-4c2e-91b9-2dfaf1434ef1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b0zJxusVVKkoQHP364ZB2R"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 293}, "_children": [], "_active": true, "_components": [{"__id__": 304}, {"__id__": 305}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 25.957, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13fTq6kLlG3Jt9XxmqQ5I/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 13.57598876953125, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "694JVe2LVIsp924yVFDvY1"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "7ehyzH8+NF7YTBbboxe2u6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 293}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "47MVZuI4NAVpVzA/Nynu0L"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e7c7SbfkVC0YSpB6NLNSHk"}, {"__type__": "cc.Node", "_name": "item5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 180}, "_children": [{"__id__": 309}, {"__id__": 312}, {"__id__": 315}, {"__id__": 318}, {"__id__": 321}, {"__id__": 324}], "_active": true, "_components": [{"__id__": 338}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -1.5, "y": -55.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eeZnceJPxOsr7u5nirGdtc"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 310}, {"__id__": 311}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4f/7oWBYJOl7n6mb0jPBEl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e9C79d3qlBQqXGhpWbqnq6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73a2236a-605a-4770-aa45-edafb7ff4dd3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "fdLz6UbPtAKbO1Iem7m2bY"}, {"__type__": "cc.Node", "_name": "T-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 313}, {"__id__": 314}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 64.201, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ccDT9ICTZASJFHiq1rC/m/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 94.77796936035156, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "75JktIonFP4auKg/28CmAg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x1000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "b6B8RhJ9BM84buREnbIbY+"}, {"__type__": "cc.Node", "_name": "bg11", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 316}, {"__id__": 317}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "63/SM+l9RGB6X7BRSo2x4p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "24M/xDobdGUpn5FhJTRjfY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6f1bc8b2-3b6b-4723-88e7-9cdc4d92f346@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "8fKIs+GP5GfJNS58BpmmnY"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 319}, {"__id__": 320}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -43.12, "y": -81.767, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8baOWtIAJGPr5hegvZN9iA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c974f7FC1CI74MOmG0Caf7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ab1fddc-3b5f-4920-9c37-bdf1ae4c2ec5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a5J1QhLyVKbLdnIJdT10vs"}, {"__type__": "cc.Node", "_name": "M-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 322}, {"__id__": 323}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 16.816, "y": -83.164, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "39EpgrRshPY4kIQG1+h8uS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 321}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 78.06497192382812, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "36eICAd0JCc7zZAZ4KU9HD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 321}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "9agygyEoBOpofYvgT1zhgG"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [{"__id__": 325}, {"__id__": 328}, {"__id__": 331}, {"__id__": 334}], "_active": false, "_components": [{"__id__": 337}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 41.02, "y": 111.539, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f1LZ1b5dBCLrh7y6AgfS/z"}, {"__type__": "cc.Node", "_name": "bg15", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 324}, "_children": [], "_active": true, "_components": [{"__id__": 326}, {"__id__": 327}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c1UW+QKdRFHYxEESDULX8z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 325}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "08GbXKCE5G7rVe0OwjSysn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 325}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ef15fdd-10ee-4555-b7b6-d1ef48d39ddb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e0l+TxkthNorkr9pi5IqiU"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 324}, "_children": [], "_active": true, "_components": [{"__id__": 329}, {"__id__": 330}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -16.867, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7eKsO55lhDvIAaGAG/ohmh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2eiHRAuRpGDrYJKbayusj4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d54b8bCU5NM6WdGQIgJYMm"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 324}, "_children": [], "_active": true, "_components": [{"__id__": 332}, {"__id__": 333}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 7.875, "y": 0.885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ceI0SUOopLBLlfY22ndJf8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 331}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f7EKEghJFOwbH++hkFwx0y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 331}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "44b9e19f-b2c6-4c2e-91b9-2dfaf1434ef1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "42H1vVMMZDT4hqEK4hcw+e"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 324}, "_children": [], "_active": true, "_components": [{"__id__": 335}, {"__id__": 336}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 25.957, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13hnM/p3tP8KdrbPkUtAu4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 334}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 13.57598876953125, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bbvh81XVFPUog98q823b7p"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 334}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "0coQ+KZgtITKYpvkHo1lR3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 324}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "39EpJY7IdNUo7y++6dDGH+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cfgCWqyqpGoavulBxk5/yk"}, {"__type__": "cc.Node", "_name": "item6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 180}, "_children": [{"__id__": 340}, {"__id__": 343}, {"__id__": 346}, {"__id__": 349}, {"__id__": 352}, {"__id__": 355}], "_active": true, "_components": [{"__id__": 369}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 185.5, "y": -55.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "54wL89v4ZJRLwwHtK33ckf"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 341}, {"__id__": 342}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "06PIxMISRHV64zIpSlskly"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 340}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e2geLWplJNbJvgc4tA4RkV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 340}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73a2236a-605a-4770-aa45-edafb7ff4dd3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "1eHAUYWiJCPIinIRLcHO0D"}, {"__type__": "cc.Node", "_name": "T-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 344}, {"__id__": 345}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 64.201, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "35ZVGn04pHTrxf+Nt8ZFYp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 343}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 98.98995971679688, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3eMOjKgudCq4duDm9a5FkH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 343}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x2400", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "4bbghS1DhIb7TUEbTJaSua"}, {"__type__": "cc.Node", "_name": "bg12", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 347}, {"__id__": 348}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f3VDkTiVNO15VBoTCCGur2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 346}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ac0IydOxZAyZjs2+5xLOTq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 346}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cbe8a552-e535-45ad-a89c-6462ec53ffa4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "6e+bZ65pNLeIKBe7GaUHIK"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 350}, {"__id__": 351}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -43.372, "y": -79.933, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3754XlubZPwZjWjjijihze"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43errmtKxNW6a09uJr3Qk6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ab1fddc-3b5f-4920-9c37-bdf1ae4c2ec5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f1fix5kmJJvZRXTZdV0vGK"}, {"__type__": "cc.Node", "_name": "M-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 353}, {"__id__": 354}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 19.399, "y": -81.331, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "68SgfzMiVJLaAxj0R8T7uM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 82.27696228027344, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "104mj2gK9NX4WqsVZkoQMg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "2400", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "3e6hYUF/NFrpn8B0Kn4XKG"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [{"__id__": 356}, {"__id__": 359}, {"__id__": 362}, {"__id__": 365}], "_active": false, "_components": [{"__id__": 368}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 22.836, "y": 111.539, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8cn9w23FlINr9xglbArG72"}, {"__type__": "cc.Node", "_name": "bg16", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 355}, "_children": [], "_active": true, "_components": [{"__id__": 357}, {"__id__": 358}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8bGy6PwpdHeaiYXf6+Fm4q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 117, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c9rYqceMpGsIMD+URm57yO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6b643445-7cb8-4442-a213-e2034e2f3b4a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "deKIZoiR9EU7zRUgPPzUKm"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 355}, "_children": [], "_active": true, "_components": [{"__id__": 360}, {"__id__": 361}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -39.392, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e7utR8PypJUrE2ez8UKhPZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cdBmjajBdDBbe1qWsI6wxE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "20M8MVVdJP84jkILhbXw2P"}, {"__type__": "cc.Node", "_name": "bg13", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 355}, "_children": [], "_active": true, "_components": [{"__id__": 363}, {"__id__": 364}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -17.799, "y": 0.885, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dcl6fncwFKo4FKp7ZV1+TQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "63pBy+DaNEOJEdklOkF0xH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "44b9e19f-b2c6-4c2e-91b9-2dfaf1434ef1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b2uisDOJZJ4JZd7vEI27Iu"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 355}, "_children": [], "_active": true, "_components": [{"__id__": 366}, {"__id__": 367}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 24.262, "y": 2.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3eXH1o6XNJ+ZlsflCN1Sdx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 365}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 61.770965576171875, "height": 30.46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5e0WkO5MdKk7xk1E1UUmlz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 365}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "2400", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 21, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "0eT3o3V51Br5LwFpQ8WEUd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 355}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 117, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1dSz0QeY5Lyph/dT1dRs9w"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "751yeb+kxACoVnlGJqfNj3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 540, "height": 600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0exMWQJ0hFqLLrXwgm+KBl"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 24, "_spacingY": 24, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 2, "_constraintNum": 3, "_affectedByScale": false, "_isAlign": false, "_id": "4fvpO1BsBDKZ2lPfreWY98"}, {"__type__": "cc.Node", "_name": "D-Button", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [{"__id__": 373}, {"__id__": 376}, {"__id__": 382}, {"__id__": 385}], "_active": true, "_components": [{"__id__": 388}, {"__id__": 389}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -336.672, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "975CJaaeJMk5Q6VoYdnh9V"}, {"__type__": "cc.Node", "_name": "bg5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 372}, "_children": [], "_active": true, "_components": [{"__id__": 374}, {"__id__": 375}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "39wSJthg5BW5Si1M21DsIH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 472, "height": 142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "14pNOPQIFMaL1ucL+3d2aq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f9548785-229c-430e-ac3a-5a1c1e2aafa6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "17pyAh4nJFBKWOJDOoG+IC"}, {"__type__": "cc.Node", "_name": "bg14", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 372}, "_children": [{"__id__": 377}], "_active": true, "_components": [{"__id__": 380}, {"__id__": 381}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -19.627, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c28yQAUj1PpZk/AlSXIxhr"}, {"__type__": "cc.Node", "_name": "bg6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 376}, "_children": [], "_active": true, "_components": [{"__id__": 378}, {"__id__": 379}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -43.298, "y": -1.428, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "530XKp6gRFuphJKiwDw+cH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 377}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a2Xw8+wyFJeLyh7wTPT0HO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 377}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ab1fddc-3b5f-4920-9c37-bdf1ae4c2ec5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e90F9la45Bj5IIxmwkYNle"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 165, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "adRq1tqx5F+ZQLrzHTzILJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bcbdb9a9-b5c3-4d26-9dfa-5fe3b4a8080c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "1f9k8bn0xFOqv+ZJKf8Euk"}, {"__type__": "cc.Node", "_name": "D-Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 372}, "_children": [], "_active": true, "_components": [{"__id__": 383}, {"__id__": 384}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 19.626, "y": -23.197, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "57frgZtmxAsKx/4TLKxNkB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 27.250991821289062, "height": 42.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2dBe/A+RFJtLJUlgdyPkr7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 4, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "fbbAlMrtVEZaLO9I+/E2UR"}, {"__type__": "cc.Node", "_name": "D-Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 372}, "_children": [], "_active": true, "_components": [{"__id__": 386}, {"__id__": 387}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 31.044, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "64alzfUqREQq/We+M3PFra"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 385}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 49.36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e9G+BXnohNipSVZEJWezTU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 385}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "立即兑换", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 36, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -8}, "_shadowBlur": 2, "_id": "3c2pT2hf5B17QO+wCsZCCp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 446, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "74LWkhSY9DxINjfo40YWk0"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 390}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "e4qlY94gxATrGtpagrUJRf"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "exchangeTili", "customEventData": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 605, "height": 1000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a5iP7yh05FAZJoNOx/6ZAd"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 50, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "a8EJQTU9dGrpDOOirFmJ1H"}, {"__type__": "cc.Node", "_name": "Tips", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [{"__id__": 394}, {"__id__": 397}], "_active": true, "_components": [{"__id__": 400}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -392.532, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b5DUt2XEZA952DNnwvKrSm"}, {"__type__": "cc.Node", "_name": "bg15", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 393}, "_children": [], "_active": true, "_components": [{"__id__": 395}, {"__id__": 396}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -199.285, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2cnq1C9hZJp5MCbAUuD2YR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 394}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e7E/UfCUtIZohcCTEhNAgY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 394}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73f517d8-1ba8-4258-9843-fd929e184166@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "517AocBUJBYoL/11UfDfTQ"}, {"__type__": "cc.Node", "_name": "Tip-Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 393}, "_children": [], "_active": true, "_components": [{"__id__": 398}, {"__id__": 399}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 17.85, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a8mA3BAQ1HT63tQu6lkeaX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 404, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2cYfa9a0lEK5wKChKPIeCy"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 174}, "_string": "兑换成功后体力无法换回积分，请确认操作！", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "1398f9c0-4bc2-44c1-9253-9a304cf8e06e", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "47SV9wydxORa5t5n7eZ9S2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 440, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "80Px7a1hdGNrmRcM+MG9yU"}, {"__type__": "cc.Node", "_name": "Message", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 159}, "_children": [{"__id__": 402}], "_active": false, "_components": [{"__id__": 405}, {"__id__": 406}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 93.604, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "58UiLN7btP8aV5jxxuSSRa"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 401}, "_children": [], "_active": true, "_components": [{"__id__": 403}, {"__id__": 404}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "949l3OIpFDJ73NjwWZDjhT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 402}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 38.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b2ld4lwVZLKqacaHnVvBbo"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 402}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "兑换成功！", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "0fDj81wddB7Kq/ORzFjwqw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "33fhk2yPZPupjNFQ7WoX7L"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 221}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "16kRA1mz1P/6Au39fr9dU6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6siJ5p2JO2JjDI6gTNi3F"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "10LypHjKlIAJ68tW60fr/G"}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 410}, {"__id__": 411}, {"__id__": 412}, {"__id__": 413}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f1U5l+rIFD0Jak6irlmehf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f9lWuG6IxJ35ef/U3K2vqN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d3023cca-544d-45b6-a74c-199cd37e53ec@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bfF328k6pLVaJGdme/DM9k"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "2evTLWZRVEDLpbJndyGCws"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "d18f3993-3898-403f-92b2-5cf8744b173f", "__expectedType__": "cc.AudioClip"}, "_loop": true, "_playOnAwake": false, "_volume": 0.5, "_id": "67pJLSldxCFLIz4tdIce/U"}, {"__type__": "cc.Node", "_name": "Fragment-Tips", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 415}, {"__id__": 418}, {"__id__": 421}], "_active": false, "_components": [{"__id__": 424}, {"__id__": 425}, {"__id__": 426}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 356.029, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "96kGgvD0hMArhn5wXybvoy"}, {"__type__": "cc.Node", "_name": "bg18", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 414}, "_children": [], "_active": true, "_components": [{"__id__": 416}, {"__id__": 417}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -187, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "82DOmQtcdJfr/EN5oyoxaN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 415}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "57DA50/W5GKYqalQY9rBPv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 415}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9c7310bc-d90b-4586-abff-554a82dfa04e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6ca/ldQptCrIsGVW5a8W5X"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 414}, "_children": [], "_active": true, "_components": [{"__id__": 419}, {"__id__": 420}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -18.100006103515625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ea0HwYSQBMIJO+c2yFGLlf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 418}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 275.79998779296875, "height": 35.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "827tKK9exCVbKx0l5GDDCi"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 418}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "钢盔·熊墩墩(坦克熊)碎片", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "1398f9c0-4bc2-44c1-9253-9a304cf8e06e", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "b6mHZpevdH55YdwyzrskHP"}, {"__type__": "cc.Node", "_name": "Number", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 414}, "_children": [], "_active": true, "_components": [{"__id__": 422}, {"__id__": 423}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 149.1874771118164, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "37Pk5tyq9FPafiNfF1lQlU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 34.77497863769531, "height": 35.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e5sf3Rc15HuaAqNg3OY1KP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 212, "b": 11, "a": 255}, "_string": "x2", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "7cQkcEyyFDz5zWhq0PpAG+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 414}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f72knHhmxL9afGE7NyS8g2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 414}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "f2d520f5-267d-4808-a58d-755a5d09e7a8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "0eB90uE5FO/bc1erFPNfwg"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 414}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 100, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 12, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "05oVBycVdMIpWGMrI+Z0vu"}, {"__type__": "cc.Node", "_name": "My-Log", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 428}, {"__id__": 432}, {"__id__": 436}, {"__id__": 448}, {"__id__": 476}], "_active": false, "_components": [{"__id__": 505}, {"__id__": 506}, {"__id__": 507}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3bAnNxcuRNkrW/PaoYPXRv"}, {"__type__": "cc.Node", "_name": "bg1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [], "_active": true, "_components": [{"__id__": 429}, {"__id__": 430}, {"__id__": 431}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -360, "y": -640, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "32jyxZjBZDA7XFbrcgwVv9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": "399wS3/YBLeL9qAZwdJfUb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "535464e4-7488-45ef-85a0-4f28dec11a55@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6cpGBUOklD47umKJ+QtTTN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "32jKIgPx5EG6/tkKIUkMI+"}, {"__type__": "cc.Node", "_name": "bg4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [], "_active": true, "_components": [{"__id__": 433}, {"__id__": 434}, {"__id__": 435}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 221.46699999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bfz/Pu7UNH2q0bp5s4fgdu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 432}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 291, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eaFY+eLDtHIaTTIgTv9IXS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 432}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c2690623-e19f-4cd8-adf9-a728f60fef53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3fv84vVMVOELljt6NFD4je"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 432}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 401.533, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "a9XSausVdAx5Cat8ugb74w"}, {"__type__": "cc.Node", "_name": "Header", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [{"__id__": 437}, {"__id__": 443}], "_active": true, "_components": [{"__id__": 446}, {"__id__": 447}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 511, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33iQsysONK6aCzOw/CRTl5"}, {"__type__": "cc.Node", "_name": "icon3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 436}, "_children": [], "_active": true, "_components": [{"__id__": 438}, {"__id__": 439}, {"__id__": 440}, {"__id__": 441}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -305.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "aeyJ1bTlNGKZby8O1Itl+1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ecLDWWASpG2KWxrmuUfalP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7030b381-a0e9-4b55-84dc-bc14033fb328@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "dcE/vvZmtH54UsAsaBDKU5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": null, "_alignFlags": 8, "_target": null, "_left": 21, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "d40sVUlkxKJLFYgMC3oe3B"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 442}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.8, "_target": null, "_id": "22FkdK5YdEra1GepgPlrMR"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 427}, "component": "", "_componentId": "de5057eYIpJDKKR254WyPrS", "handler": "CloseLog", "customEventData": ""}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 436}, "_children": [], "_active": true, "_components": [{"__id__": 444}, {"__id__": 445}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "49QdVriqREdIY0c2ygwS3x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 48.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4fmqDeBDFAnYChKUVTCce9"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "我的记录", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "_shadowBlur": 2, "_id": "d1AYZNRfhG3ovyJ7997DBN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 436}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c8BPrQJJxJjbP1mERGI+JE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 436}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 100, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "07ML3sJF1FwJmM70WpMvN9"}, {"__type__": "cc.Node", "_name": "TotalBox", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [{"__id__": 449}, {"__id__": 452}, {"__id__": 463}], "_active": true, "_components": [{"__id__": 474}, {"__id__": 475}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 370.197, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4ed9jJ8WZI+bdhyUGthZHs"}, {"__type__": "cc.Node", "_name": "bg2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 448}, "_children": [], "_active": true, "_components": [{"__id__": 450}, {"__id__": 451}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "37+ZuZVc1JS7QMiiukrMa6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 676, "height": 220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f3hJJmqDFBYqnIy0liOa6C"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7118f123-3df0-4d25-84c0-0417ca4b179b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e0AAxHYtNE5omNNOA15+di"}, {"__type__": "cc.Node", "_name": "LeftStatsGroup", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 448}, "_children": [{"__id__": 453}, {"__id__": 456}, {"__id__": 459}], "_active": true, "_components": [{"__id__": 462}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "40E/va5CNOMJTefYv4dATl"}, {"__type__": "cc.Node", "_name": "icon2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 452}, "_children": [], "_active": true, "_components": [{"__id__": 454}, {"__id__": 455}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -272.055, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "58Ip0fApBNCq5dWkzFMuJ8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1saDOc+hNTL9/CEjfZ+hX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "977a352c-3abd-4a0c-bd62-45dbc80dc73a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ccFfwIPT9OF7b9L9oSBvXH"}, {"__type__": "cc.Node", "_name": "ValueLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 452}, "_children": [], "_active": true, "_components": [{"__id__": 457}, {"__id__": 458}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -244.404, "y": -26, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d3fV5qydFCoKyHKH2W9mjP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 456}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 154.1399383544922, "height": 52.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": "fa5bDiALFJJ4u6dUc0odUb"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 456}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 243, "g": 202, "b": 12, "a": 255}, "_string": "1694.11", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 42, "_fontSize": 42, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "e3X09TYs5CAa1fp4d++saz"}, {"__type__": "cc.Node", "_name": "TitleLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 452}, "_children": [], "_active": true, "_components": [{"__id__": 460}, {"__id__": 461}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -234.939, "y": -63, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31lj/eot9DGKLh+krUNw+l"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 459}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": "a5iQHhTl9EyIxhUgdESZIc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 459}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "累计获得", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "ddmGTnlP9KpoYFTxtaczN0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c70oNfzw1HSbExLce3NYZz"}, {"__type__": "cc.Node", "_name": "RightStatsGroup", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 448}, "_children": [{"__id__": 464}, {"__id__": 467}, {"__id__": 470}], "_active": true, "_components": [{"__id__": 473}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6fs2g7rgxBhaG5j1WjpxPU"}, {"__type__": "cc.Node", "_name": "icon3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 463}, "_children": [], "_active": true, "_components": [{"__id__": 465}, {"__id__": 466}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 81.712, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31BZL5UO9DaatoF/yAE6ay"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 464}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "97SjpcAZxKeqxs2h5olWBc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 464}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "977a352c-3abd-4a0c-bd62-45dbc80dc73a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4fH1UOj/JOXLy760uTHJ7v"}, {"__type__": "cc.Node", "_name": "TitleLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 463}, "_children": [], "_active": true, "_components": [{"__id__": 468}, {"__id__": 469}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 109.895, "y": -63, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2fQ76VxHpOfJrvDY9mF7KF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 467}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": "42Smoj2QpNTpzEggBDR0vj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 467}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "今日获得", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "e18XvCKAdKaJ78C/3aUji0"}, {"__type__": "cc.Node", "_name": "ValueLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 463}, "_children": [], "_active": true, "_components": [{"__id__": 471}, {"__id__": 472}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 111.945, "y": -21.806, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "05cusvCBNBJ4E0oYO5TRpy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 470}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 123.01795959472656, "height": 52.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": "2baviXdQpObIEiuHIei9/V"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 470}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "45.45", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 42, "_fontSize": 42, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "06xynMDsJHqoQRJHxryXqp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 463}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "16jNhSR8xN2qJwpBXBZDUm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 676, "height": 220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6e7tsd9hhNf5sGAI8C5y1c"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 159.803, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "d6Nh3X8VJNzYa6coVEup4a"}, {"__type__": "cc.Node", "_name": "RecordList", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [{"__id__": 477}], "_active": true, "_components": [{"__id__": 501}, {"__id__": 502}, {"__id__": 503}, {"__id__": 504}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -360, "y": 184.649, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "40wiFrKgdMToNaj+tehkV5"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 476}, "_children": [{"__id__": 478}], "_active": true, "_components": [{"__id__": 497}, {"__id__": 498}, {"__id__": 499}, {"__id__": 500}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 362.066, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c1aAAtlMBDiJJT2qOHy6LJ"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 477}, "_children": [{"__id__": 479}, {"__id__": 487}], "_active": true, "_components": [{"__id__": 495}, {"__id__": 496}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -343.506, "y": -26.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "df8KcMkxpMlqS+hBCGu7rS"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 478}, "_prefab": {"__id__": 480}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 479}, "asset": {"__uuid__": "aaf0f61f-8708-40a5-b8bc-3f77d445d4f5", "__expectedType__": "cc.Prefab"}, "fileId": "22zSOWCPpLm7CnMIE/aWuR", "instance": {"__id__": 481}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c3s0IKCE1PNoZOaJZXoJaC", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 482}, {"__id__": 484}, {"__id__": 485}, {"__id__": 486}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 483}, "propertyPath": ["_name"], "value": "RecordItem"}, {"__type__": "cc.TargetInfo", "localID": ["22zSOWCPpLm7CnMIE/aWuR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 483}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 483}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 483}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 478}, "_prefab": {"__id__": 488}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 487}, "asset": {"__uuid__": "aaf0f61f-8708-40a5-b8bc-3f77d445d4f5", "__expectedType__": "cc.Prefab"}, "fileId": "22zSOWCPpLm7CnMIE/aWuR", "instance": {"__id__": 489}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "00jjGiX5xEoLrripN51dBn", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 490}, {"__id__": 492}, {"__id__": 493}, {"__id__": 494}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 491}, "propertyPath": ["_name"], "value": "RecordItem"}, {"__type__": "cc.TargetInfo", "localID": ["22zSOWCPpLm7CnMIE/aWuR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 491}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -119, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 491}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 491}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 478}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 238}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": "63y93YE81Kf6N7btwIkaTd"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 478}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "2e6BbQa1BBkofjG1eOjnPj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 477}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 824.649}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": "d9Kt7AtzdD6rlTfQ/gDDrZ"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 477}, "_enabled": true, "__prefab": null, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": "35BH16L49Bha3Bk43iGPIV"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 477}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": "75PbEHJmRLRrKd8xTCyz5e"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 477}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 2.0659999999999954, "_right": -2.0659999999999954, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": "93B1uQvBRMbJVLFOoIAMQm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 476}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 824.649}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "5brR5AyyFLMaCx52QvTHjR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 476}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cdI3dWFzJIGbmEeXPWohan"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 476}, "_enabled": true, "__prefab": null, "bounceDuration": 0.23, "brake": 0.75, "elastic": false, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 478}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": "43WAj4qP9DgKPraRZUSOyK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 476}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 455.351, "_bottom": -5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": "67zLA8XhVJ14UvVK/bXwQi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e6OtxvIC5K8q+19Bs0BIsB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "00f+vgcY5D9Zq5xQFeglgH"}, {"__type__": "de5057eYIpJDKKR254WyPrS", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": null, "MyLog_Node": null, "_id": "9aEVqQwaJKWoPOKMmXvsVF"}, {"__type__": "cc.Node", "_name": "TabBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 509}, {"__id__": 512}, {"__id__": 528}, {"__id__": 536}, {"__id__": 547}, {"__id__": 558}], "_active": true, "_components": [{"__id__": 566}, {"__id__": 567}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -1, "y": -590.04, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8feoOqNmtC6Lv/8BHYNsCT"}, {"__type__": "cc.Node", "_name": "bg11", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [], "_active": true, "_components": [{"__id__": 510}, {"__id__": 511}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 1, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "42fkpjYrFDBbVXHpapdcjD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "16qNW2BbZLxacb4U3bqkPn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e4df80d7-7c74-4a1e-944c-b9bd9de9f592@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4fWFsy51hFi7dbFGTkeIyD"}, {"__type__": "cc.Node", "_name": "Jc", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [{"__id__": 513}, {"__id__": 516}, {"__id__": 519}, {"__id__": 522}], "_active": true, "_components": [{"__id__": 527}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -288.459, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "67gY44NcBOUKjCVCyZ9VdI"}, {"__type__": "cc.Node", "_name": "SpriteSplash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 512}, "_children": [], "_active": true, "_components": [{"__id__": 514}, {"__id__": 515}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 71.79, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "149bMNgqFN97WoyPGnQWo3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 513}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8dsYiE/klJ5JM8EEZJMhav"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 513}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a9SI4cxUxBMowcQ5gnNqD3"}, {"__type__": "cc.Node", "_name": "t5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 512}, "_children": [], "_active": true, "_components": [{"__id__": 517}, {"__id__": 518}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 14.964, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b0YF2b/IRD55r8sy216B/h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 516}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "02AJQCvl9Nnr/gsNZIZ3E6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 516}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d5c72047-431b-48d7-a3c5-8f198025dd62@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "caG6zn2QFM06t4rzxc/vYE"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 512}, "_children": [], "_active": true, "_components": [{"__id__": 520}, {"__id__": 521}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -27.435, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97UQC5b2BBAKRU1+AxL6ZS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "10e14sPzVFWbHmR65BUptg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 119}, "_string": "储备", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -2}, "_shadowBlur": 2, "_id": "66hGncofRMfLQc6NLT49kb"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 512}, "_children": [], "_active": true, "_components": [{"__id__": 523}, {"__id__": 524}, {"__id__": 525}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a6zg4DvHtCIKhAADGv0Hwk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 522}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e4Fyme2wVPKoChlZ2P7cQm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 522}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c5r+THD85BMbOa8k3onIyq"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 522}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 526}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 522}, "_id": "d3XrisvEhBXKEanClVenNr"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "243223R+y9C8LfOKZXSm/q1", "handler": "Jump", "customEventData": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 512}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fdIhR4slBNhbLReh95EXz7"}, {"__type__": "cc.Node", "_name": "Hc", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [{"__id__": 529}, {"__id__": 532}], "_active": true, "_components": [{"__id__": 535}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -144.706, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fCqiQ9p1NiJHwQZL0tKIp"}, {"__type__": "cc.Node", "_name": "t2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 528}, "_children": [], "_active": true, "_components": [{"__id__": 530}, {"__id__": 531}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 16.627, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "38+NPRE+5CA7fxJDcc5VF1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 529}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 51, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7bDQl1SHxEUp2Q690WUn9r"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 529}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cbd880d0-e1ec-4f62-b302-047410dfef80@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6eS/M8izdKIpBJoToHSZOI"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 528}, "_children": [], "_active": true, "_components": [{"__id__": 533}, {"__id__": 534}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -27.435, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "64vMubPh9MCYUPpZ9/r51G"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 532}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "35d3PNHsJPho6Sh12CLN+z"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 532}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 119}, "_string": "合成", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -2}, "_shadowBlur": 2, "_id": "30imeMwzZC+r9+9b7ba+VV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 528}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "87Gr2SIVRLiYS3BR3hQi7O"}, {"__type__": "cc.Node", "_name": "Home", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [{"__id__": 537}, {"__id__": 540}, {"__id__": 543}], "_active": true, "_components": [{"__id__": 546}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 10.033, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0bU4bQoJJOH54+bviQnJ8Z"}, {"__type__": "cc.Node", "_name": "bg12", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 536}, "_children": [], "_active": true, "_components": [{"__id__": 538}, {"__id__": 539}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.555, "y": 2.145, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d2XBJGCK5JZYFy97IkTwku"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 537}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "02qAXXbuBP86MN4RXBYTFx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 537}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59bbbc6d-f7bc-4e17-8a74-c88d9b4bd2c7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "78T47zQMlKo6eMLLwubC8c"}, {"__type__": "cc.Node", "_name": "t1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 536}, "_children": [], "_active": true, "_components": [{"__id__": 541}, {"__id__": 542}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.338, "y": 51.544, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "45WziXe7xJN4ZkunxS62p4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 540}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a1oum1YBdBsruyRMtPbeH5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 540}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "09ca5dc9-15b9-448e-bf5d-a00dccd4dc3c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e5lP92VlJFo5V1bKM0NEgm"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 536}, "_children": [], "_active": true, "_components": [{"__id__": 544}, {"__id__": 545}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -23.278, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f4a2MzESNP768KHdCG7Hv0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 35.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d9t27E4tlKLosjPrJv2Tud"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "首页", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -2}, "_shadowBlur": 2, "_id": "96z5Yn/+lCA4RGYubIxYj2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 536}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "75kCL0fzdBVIcbAA1JXHdD"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [{"__id__": 548}, {"__id__": 551}, {"__id__": 554}], "_active": true, "_components": [{"__id__": 557}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 161.274, "y": 0.477, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b6A9qIxzpNF5LpvEov0pah"}, {"__type__": "cc.Node", "_name": "SpriteSplash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 547}, "_children": [], "_active": true, "_components": [{"__id__": 549}, {"__id__": 550}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 66.055, "y": -0.519, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "06PZ/rmZFINI9Tf2MEfDo3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 548}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b4CHjD6BVDsZW664zJZ535"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 548}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "56LdV8xVRPto8X0bpwCNfg"}, {"__type__": "cc.Node", "_name": "t3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 547}, "_children": [], "_active": true, "_components": [{"__id__": 552}, {"__id__": 553}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 14.964, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85KE24ZlNPP63GP9ayzpSm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 551}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cdCXYFl15BwI5mUPTe4Nsi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 551}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "415fddd8-4c10-4a75-b7f4-a45bc343ee5b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bdOqQWljZFyIer8+oH9CCZ"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 547}, "_children": [], "_active": true, "_components": [{"__id__": 555}, {"__id__": 556}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -27.911999999999995, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "99VXoMuztIx4Dfb52e70sJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f2NR4urFZI3rQwzkPb9t65"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 119}, "_string": "交易", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -2}, "_shadowBlur": 2, "_id": "b1RxorHwRMzbS8pnUfXwrR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 547}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "850K2/g7lB8oBD53qcJ1iw"}, {"__type__": "cc.Node", "_name": "Mx", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [{"__id__": 559}, {"__id__": 562}], "_active": true, "_components": [{"__id__": 565}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 293.078, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f5U5VmQitKva2QsygHO/7A"}, {"__type__": "cc.Node", "_name": "t4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 558}, "_children": [], "_active": true, "_components": [{"__id__": 560}, {"__id__": 561}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 14.964, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a4vXG1tWtKQ4p/Jkaw1IEK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ddFf/j0nBB9JO3FuTNd9//"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6b9e49e4-4e95-4008-add2-195d629758d0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7b+VvF5khJebQTbkxhRTTP"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 558}, "_children": [], "_active": true, "_components": [{"__id__": 563}, {"__id__": 564}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -27.435, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "99NhaRodNAK6NO/jbark/S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 562}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 29.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "91jFuTbK5Az7mJPnXvY3Xh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 562}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 119}, "_string": "冒险", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7ac81fe4-49ce-4e66-88c2-83ff2383b5bc", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -2}, "_shadowBlur": 2, "_id": "18La8jZNJE3bNyhDL3Hb7A"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 558}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bfm2spuIZM1pfh8NDwVxtO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 508}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "42K/pgDyxLHLPrjR7L+Ce4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 508}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -0.03999999999996362, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "0cKIgIaCpJnbIrI+aoUgO7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "2e6d9d13-d514-4c50-b664-2e319d90abb9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 479}, {"__id__": 487}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 573}, "shadows": {"__id__": 574}, "_skybox": {"__id__": 575}, "fog": {"__id__": 576}, "octree": {"__id__": 577}, "skin": {"__id__": 578}, "lightProbeInfo": {"__id__": 579}, "postSettings": {"__id__": 580}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]
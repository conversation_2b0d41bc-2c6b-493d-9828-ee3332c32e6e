/**
 * 统一定时器管理工具类
 * 用于管理组件中的所有定时器，避免内存泄漏和重复创建
 */
export class TimerManager {
	private timers: Map<string, number> = new Map(); // 存储所有定时器
	private timeouts: Map<string, number> = new Map(); // 存储所有延时器
	private isDestroyed: boolean = false; // 标记是否已销毁
	private autoCleanupEnabled: boolean = true; // 是否启用自动清理

	/**
	 * 构造函数
	 * @param autoCleanup 是否启用自动清理（默认启用）
	 */
	constructor(autoCleanup: boolean = true) {
		this.autoCleanupEnabled = autoCleanup;
		console.log('定时器管理器已初始化');
	}

	/**
	 * 创建或重置一个定时器
	 * @param key 定时器的唯一标识
	 * @param callback 回调函数
	 * @param interval 间隔时间（毫秒）
	 */
	setInterval(key: string, callback: () => void, interval: number): void {
		// 检查是否已销毁
		if (this.isDestroyed) {
			console.warn(`定时器管理器已销毁，无法创建定时器: ${key}`);
			return;
		}

		// 先清理已存在的定时器
		this.clearInterval(key);

		// 创建新的定时器
		const timerId = setInterval(() => {
			// 检查是否已销毁
			if (this.isDestroyed) {
				this.clearInterval(key);
				return;
			}

			try {
				callback();
			} catch (error) {
				console.error(`定时器 ${key} 执行回调时出错:`, error);
				if (this.autoCleanupEnabled) {
					this.clearInterval(key);
				}
			}
		}, interval);

		this.timers.set(key, timerId);
		console.log(`定时器 ${key} 已启动，间隔: ${interval}ms`);
	}

	/**
	 * 创建或重置一个延时器
	 * @param key 延时器的唯一标识
	 * @param callback 回调函数
	 * @param delay 延时时间（毫秒）
	 */
	setTimeout(key: string, callback: () => void, delay: number): void {
		// 检查是否已销毁
		if (this.isDestroyed) {
			console.warn(`定时器管理器已销毁，无法创建延时器: ${key}`);
			return;
		}

		// 先清理已存在的延时器
		this.clearTimeout(key);

		// 创建新的延时器
		const timeoutId = setTimeout(() => {
			// 检查是否已销毁
			if (this.isDestroyed) {
				this.timeouts.delete(key);
				return;
			}

			try {
				callback();
			} catch (error) {
				console.error(`延时器 ${key} 执行回调时出错:`, error);
			} finally {
				// 执行完成后自动清理
				this.timeouts.delete(key);
			}
		}, delay);

		this.timeouts.set(key, timeoutId);
		console.log(`延时器 ${key} 已启动，延时: ${delay}ms`);
	}

	/**
	 * 清理指定的定时器
	 * @param key 定时器的唯一标识
	 */
	clearInterval(key: string): void {
		const timerId = this.timers.get(key);
		if (timerId !== undefined) {
			clearInterval(timerId);
			this.timers.delete(key);
			console.log(`定时器 ${key} 已清理`);
		}
	}

	/**
	 * 清理指定的延时器
	 * @param key 延时器的唯一标识
	 */
	clearTimeout(key: string): void {
		const timeoutId = this.timeouts.get(key);
		if (timeoutId !== undefined) {
			clearTimeout(timeoutId);
			this.timeouts.delete(key);
			console.log(`延时器 ${key} 已清理`);
		}
	}

	/**
	 * 检查指定定时器是否存在
	 * @param key 定时器的唯一标识
	 */
	hasInterval(key: string): boolean {
		return this.timers.has(key);
	}

	/**
	 * 检查指定延时器是否存在
	 * @param key 延时器的唯一标识
	 */
	hasTimeout(key: string): boolean {
		return this.timeouts.has(key);
	}

	/**
	 * 获取活跃的定时器数量
	 */
	getActiveIntervalCount(): number {
		return this.timers.size;
	}

	/**
	 * 获取活跃的延时器数量
	 */
	getActiveTimeoutCount(): number {
		return this.timeouts.size;
	}

	/**
	 * 清理所有定时器
	 */
	clearAllIntervals(): void {
		this.timers.forEach((timerId, key) => {
			clearInterval(timerId);
			console.log(`定时器 ${key} 已清理`);
		});
		this.timers.clear();
		console.log('所有定时器已清理');
	}

	/**
	 * 清理所有延时器
	 */
	clearAllTimeouts(): void {
		this.timeouts.forEach((timeoutId, key) => {
			clearTimeout(timeoutId);
			console.log(`延时器 ${key} 已清理`);
		});
		this.timeouts.clear();
		console.log('所有延时器已清理');
	}

	/**
	 * 清理所有定时器和延时器
	 */
	clearAll(): void {
		this.clearAllIntervals();
		this.clearAllTimeouts();
	}

	/**
	 * 销毁定时器管理器
	 * 清理所有定时器并标记为已销毁状态
	 */
	destroy(): void {
		this.isDestroyed = true;
		this.clearAll();
		console.log('定时器管理器已销毁');
	}

	/**
	 * 检查是否已销毁
	 */
	getIsDestroyed(): boolean {
		return this.isDestroyed;
	}

	/**
	 * 设置自动清理开关
	 * @param enabled 是否启用自动清理
	 */
	setAutoCleanup(enabled: boolean): void {
		this.autoCleanupEnabled = enabled;
		console.log(`自动清理已${enabled ? '启用' : '禁用'}`);
	}

	/**
	 * 获取自动清理状态
	 */
	getAutoCleanup(): boolean {
		return this.autoCleanupEnabled;
	}

	/**
	 * 获取所有活跃的定时器键名
	 */
	getActiveIntervalKeys(): string[] {
		return Array.from(this.timers.keys());
	}

	/**
	 * 获取所有活跃的延时器键名
	 */
	getActiveTimeoutKeys(): string[] {
		return Array.from(this.timeouts.keys());
	}

	/**
	 * 打印当前所有活跃的定时器信息（调试用）
	 */
	debugPrint(): void {
		console.log('=== 定时器管理器状态 ===');
		console.log(`活跃定时器数量: ${this.timers.size}`);
		console.log(`活跃延时器数量: ${this.timeouts.size}`);

		if (this.timers.size > 0) {
			console.log('活跃定时器:', Array.from(this.timers.keys()));
		}

		if (this.timeouts.size > 0) {
			console.log('活跃延时器:', Array.from(this.timeouts.keys()));
		}
		console.log('========================');
	}
}

// 全局定时器管理器注册表
const globalTimerManagers: Set<TimerManager> = new Set();

/**
 * 全局定时器管理器实例
 * 用于管理全局的定时器，如果需要组件级别的管理，请创建新的实例
 */
export const globalTimerManager = new TimerManager();

/**
 * 创建新的定时器管理器实例
 * 通常每个组件使用一个独立的实例
 */
export function createTimerManager(): TimerManager {
	const manager = new TimerManager();
	// 注册到全局管理器列表
	globalTimerManagers.add(manager);
	return manager;
}

/**
 * 清理所有定时器管理器
 * 在应用退出或页面卸载时调用
 */
export function clearAllTimerManagers(): void {
	console.log(`清理 ${globalTimerManagers.size + 1} 个定时器管理器`);

	// 清理全局管理器
	globalTimerManager.destroy();

	// 清理所有注册的管理器
	globalTimerManagers.forEach((manager) => {
		manager.destroy();
	});
	globalTimerManagers.clear();
}

/**
 * 获取所有活跃的定时器管理器统计信息
 */
export function getTimerManagerStats(): {
	totalManagers: number;
	totalIntervals: number;
	totalTimeouts: number;
} {
	let totalIntervals = globalTimerManager.getActiveIntervalCount();
	let totalTimeouts = globalTimerManager.getActiveTimeoutCount();

	globalTimerManagers.forEach((manager) => {
		totalIntervals += manager.getActiveIntervalCount();
		totalTimeouts += manager.getActiveTimeoutCount();
	});

	return {
		totalManagers: globalTimerManagers.size + 1,
		totalIntervals,
		totalTimeouts,
	};
}

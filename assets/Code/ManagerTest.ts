import { _decorator, Component, Node, Label, sp, AudioSource } from 'cc';
import { TimerManager, createTimerManager } from './TimerManager';
import { AnimationManager, createAnimationManager } from './AnimationManager';
import { AudioManager, AudioType, createAudioManager } from './AudioManager';
import { NumberAnimationManager } from './NumberAnimationUtil';

const { ccclass, property } = _decorator;

/**
 * 管理器测试组件
 * 用于测试TimerManager、AnimationManager和AudioManager的功能
 */
@ccclass('ManagerTest')
export class ManagerTest extends Component {
	@property(Label)
	testLabel: Label = null;

	@property(sp.Skeleton)
	testSpine: sp.Skeleton = null;

	@property(Node)
	audioNode: Node = null;

	private timerManager: TimerManager | null = null;
	private animationManager: AnimationManager | null = null;
	private audioManager: AudioManager | null = null;
	private numberAnimationManager: NumberAnimationManager | null = null;

	start() {
		this.initializeManagers();
		this.runTests();
	}

	/**
	 * 初始化管理器
	 */
	private initializeManagers(): void {
		console.log('=== 开始初始化管理器 ===');

		// 初始化定时器管理器
		this.timerManager = createTimerManager();
		console.log('✓ TimerManager 初始化完成');

		// 初始化动画管理器
		if (this.testSpine) {
			this.animationManager = createAnimationManager(this.testSpine);
			console.log('✓ AnimationManager 初始化完成');
		} else {
			console.warn('⚠ 未找到Spine组件，跳过AnimationManager初始化');
		}

		// 初始化音频管理器
		this.audioManager = createAudioManager();
		if (this.audioNode) {
			this.audioManager.registerAudioFromNode(AudioType.BACKGROUND, this.audioNode, 0.5, true);
			console.log('✓ AudioManager 初始化完成');
		} else {
			console.warn('⚠ 未找到音频节点，跳过音频注册');
		}

		// 初始化数字动画管理器
		this.numberAnimationManager = new NumberAnimationManager();
		if (this.testLabel) {
			this.numberAnimationManager.createAnimation('test', this.testLabel, 0);
			console.log('✓ NumberAnimationManager 初始化完成');
		} else {
			console.warn('⚠ 未找到Label组件，跳过数字动画初始化');
		}

		console.log('=== 管理器初始化完成 ===\n');
	}

	/**
	 * 运行测试
	 */
	private runTests(): void {
		console.log('=== 开始功能测试 ===');

		this.testTimerManager();
		this.testAnimationManager();
		this.testAudioManager();
		this.testNumberAnimationManager();

		console.log('=== 功能测试完成 ===\n');
	}

	/**
	 * 测试定时器管理器
	 */
	private testTimerManager(): void {
		if (!this.timerManager) {
			console.error('❌ TimerManager 未初始化');
			return;
		}

		console.log('--- 测试 TimerManager ---');

		// 测试定时器
		let counter = 0;
		this.timerManager.setInterval('testTimer', () => {
			counter++;
			console.log(`定时器执行次数: ${counter}`);
			
			if (counter >= 3) {
				this.timerManager?.clearInterval('testTimer');
				console.log('✓ 定时器测试完成');
			}
		}, 1000);

		// 测试延时器
		this.timerManager.setTimeout('testTimeout', () => {
			console.log('✓ 延时器测试完成');
		}, 2000);

		// 测试状态查询
		console.log(`活跃定时器数量: ${this.timerManager.getActiveIntervalCount()}`);
		console.log(`活跃延时器数量: ${this.timerManager.getActiveTimeoutCount()}`);
	}

	/**
	 * 测试动画管理器
	 */
	private testAnimationManager(): void {
		if (!this.animationManager) {
			console.log('⚠ AnimationManager 未初始化，跳过测试');
			return;
		}

		console.log('--- 测试 AnimationManager ---');

		// 测试播放攻击动画
		this.animationManager.playAttackAnimation();
		console.log('✓ 播放攻击动画');

		// 5秒后测试播放睡觉动画
		if (this.timerManager) {
			this.timerManager.setTimeout('testSleepAnimation', () => {
				if (this.animationManager) {
					this.animationManager.playSleepAnimation();
					console.log('✓ 播放睡觉动画');
				}
			}, 5000);
		}
	}

	/**
	 * 测试音频管理器
	 */
	private testAudioManager(): void {
		if (!this.audioManager) {
			console.error('❌ AudioManager 未初始化');
			return;
		}

		console.log('--- 测试 AudioManager ---');

		// 测试音乐开关
		const musicEnabled = this.audioManager.getMusicEnabled();
		console.log(`当前音乐状态: ${musicEnabled ? '开启' : '关闭'}`);

		// 测试切换音乐状态
		const newState = this.audioManager.toggleMusic();
		console.log(`切换后音乐状态: ${newState ? '开启' : '关闭'}`);

		// 测试播放背景音乐
		if (this.audioManager.playBackgroundMusic()) {
			console.log('✓ 背景音乐播放成功');
		} else {
			console.log('⚠ 背景音乐播放失败（可能是音乐关闭或未注册音频源）');
		}

		console.log(`注册的音频源数量: ${this.audioManager.getAudioSourceCount()}`);
	}

	/**
	 * 测试数字动画管理器
	 */
	private testNumberAnimationManager(): void {
		if (!this.numberAnimationManager) {
			console.error('❌ NumberAnimationManager 未初始化');
			return;
		}

		console.log('--- 测试 NumberAnimationManager ---');

		const animation = this.numberAnimationManager.getAnimation('test');
		if (animation) {
			// 测试数字动画
			animation.startAnimation(12345, 2, () => {
				console.log('✓ 数字动画播放完成');
			});
			console.log('✓ 数字动画开始播放');
		} else {
			console.log('⚠ 未找到测试动画实例');
		}
	}

	/**
	 * 更新方法
	 */
	update(deltaTime: number): void {
		// 更新数字动画
		if (this.numberAnimationManager) {
			this.numberAnimationManager.updateAll(deltaTime);
		}
	}

	/**
	 * 销毁时清理资源
	 */
	onDestroy(): void {
		console.log('=== 开始清理管理器 ===');

		if (this.timerManager) {
			this.timerManager.destroy();
			this.timerManager = null;
			console.log('✓ TimerManager 已清理');
		}

		if (this.animationManager) {
			this.animationManager.destroy();
			this.animationManager = null;
			console.log('✓ AnimationManager 已清理');
		}

		if (this.audioManager) {
			this.audioManager.destroy();
			this.audioManager = null;
			console.log('✓ AudioManager 已清理');
		}

		if (this.numberAnimationManager) {
			this.numberAnimationManager.clear();
			this.numberAnimationManager = null;
			console.log('✓ NumberAnimationManager 已清理');
		}

		console.log('=== 管理器清理完成 ===');
	}

	/**
	 * 手动触发测试（可在编辑器中调用）
	 */
	public manualTest(): void {
		console.log('=== 手动测试开始 ===');
		this.runTests();
	}

	/**
	 * 打印管理器状态（调试用）
	 */
	public printManagerStatus(): void {
		console.log('=== 管理器状态 ===');
		
		if (this.timerManager) {
			this.timerManager.debugPrint();
		}
		
		if (this.animationManager) {
			console.log(`AnimationManager - 当前动画: ${this.animationManager.getCurrentAnimation()}`);
			console.log(`AnimationManager - 是否播放中: ${this.animationManager.isAnimationPlaying()}`);
		}
		
		if (this.audioManager) {
			console.log(`AudioManager - 音乐状态: ${this.audioManager.getMusicEnabled()}`);
			console.log(`AudioManager - 音频源数量: ${this.audioManager.getAudioSourceCount()}`);
		}
		
		console.log('==================');
	}
}

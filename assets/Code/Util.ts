/**
 * 工具类 - 本地存储相关功能
 */
export class LocalStorageUtil {
  /**
   * 获取本地存储的值
   * @param key 存储键名
   * @param defaultValue 默认值
   * @returns 存储的值或默认值
   */
  static getItem<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      return item !== null ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`获取本地存储失败 [${key}]:`, error);
      return defaultValue;
    }
  }

  /**
   * 设置本地存储的值
   * @param key 存储键名
   * @param value 要存储的值
   */
  static setItem<T>(key: string, value: T): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`设置本地存储失败 [${key}]:`, error);
    }
  }

  /**
   * 删除本地存储的值
   * @param key 存储键名
   */
  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`删除本地存储失败 [${key}]:`, error);
    }
  }

  /**
   * 检查本地存储中是否存在指定键
   * @param key 存储键名
   * @returns 是否存在
   */
  static hasItem(key: string): boolean {
    try {
      return localStorage.getItem(key) !== null;
    } catch (error) {
      console.error(`检查本地存储失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 清空所有本地存储
   */
  static clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error("清空本地存储失败:", error);
    }
  }
}

/**
 * 音乐设置相关的存储键名
 */
export const MUSIC_STORAGE_KEY = "musicEnabled";

/**
 * 音乐设置工具类
 */
export class MusicStorageUtil {
  /**
   * 获取音乐开关状态
   * @returns 音乐是否开启
   */
  static getMusicEnabled(): boolean {
    return LocalStorageUtil.getItem(MUSIC_STORAGE_KEY, true);
  }

  /**
   * 保存音乐开关状态
   * @param enabled 是否开启音乐
   */
  static saveMusicEnabled(enabled: boolean): void {
    LocalStorageUtil.setItem(MUSIC_STORAGE_KEY, enabled);
  }
}

/**
 * 数字格式化工具类
 */
export class NumberFormatUtil {
  /**
   * 格式化数字，保留小数并添加万字单位
   * @param value 要格式化的数值
   * @returns 格式化后的字符串
   */
  static formatNumber(value: number): string {
    // 检查是否为整数
    if (Number.isInteger(value)) {
      // 整数显示
      if (value >= 100000) {
        return (value / 10000).toFixed(2) + "万";
      } else {
        return value.toString();
      }
    } else {
      // 有小数时显示指定的小数位数
      if (value >= 100000) {
        return (value / 10000).toFixed(2) + "万";
      } else {
        return value.toFixed(2);
      }
    }
  }

  /**
   * 格式化数字，保留指定小数位数并添加万字单位
   * @param value 要格式化的数值
   * @param decimalPlaces 小数位数，默认2位
   * @returns 格式化后的字符串
   */
  static formatNumberWithDecimals(
    value: number,
    decimalPlaces: number = 2
  ): string {
    if (value >= 100000) {
      return (value / 10000).toFixed(decimalPlaces) + "万";
    } else {
      if (Number.isInteger(value)) {
        return value.toString();
      } else {
        return value.toFixed(decimalPlaces);
      }
    }
  }
}

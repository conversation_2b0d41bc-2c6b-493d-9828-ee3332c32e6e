// 请求工具封装
class RequestTool {
  private baseURL: string;
  private token: string | null = null;

  // https://mtjf.top/api
  // https://ybty.club/v9/api
  constructor(baseURL: string = "https://ybty.club/v9/api") {
    this.baseURL = baseURL;
    this.initToken();
  }

  // 初始化 token
  private initToken(): void {
    this.token = this.getTokenFromUrl();
  }

  // 从 URL 获取 token
  private getTokenFromUrl(): string | null {
    const search = window.location.search;
    if (!search) return null;
    const params = new URLSearchParams(search);
    return params.get("token");
  }

  // 请求拦截器
  private requestInterceptor(
    url: string,
    options: RequestInit
  ): { url: string; options: RequestInit } {
    // 添加 token 到 header
    if (this.token) {
      options.headers = {
        ...options.headers,
        Authorization: this.token,
        "Content-Type": "application/json",
      };
    }

    // 拼接完整 URL
    const fullUrl = url.startsWith("http") ? url : `${this.baseURL}${url}`;
    return { url: fullUrl, options };
  }

  // 响应拦截器
  private responseInterceptor(response: Response): Promise<any> {
    if (!response.ok) {
      // 处理 HTTP 错误
      const error = new Error(
        `HTTP ${response.status}: ${response.statusText}`
      );
      console.error("响应拦截器 - 错误:", error);
      return Promise.reject(error);
    }

    // 尝试解析 JSON
    return response.json().catch((error) => {
      console.error("响应拦截器 - JSON 解析错误:", error);
      return Promise.reject(error);
    });
  }

  // GET 请求
  async get(url: string, options: RequestInit = {}): Promise<any> {
    const { url: fullUrl, options: finalOptions } = this.requestInterceptor(
      url,
      {
        method: "GET",
        ...options,
      }
    );

    try {
      const response = await fetch(fullUrl, finalOptions);
      return await this.responseInterceptor(response);
    } catch (error) {
      console.error("GET 请求失败:", error);
      throw error;
    }
  }

  // POST 请求
  async post(
    url: string,
    data: any = {},
    options: RequestInit = {}
  ): Promise<any> {
    const { url: fullUrl, options: finalOptions } = this.requestInterceptor(
      url,
      {
        method: "POST",
        body: JSON.stringify(data),
        ...options,
      }
    );

    try {
      const response = await fetch(fullUrl, finalOptions);
      return await this.responseInterceptor(response);
    } catch (error) {
      console.error("POST 请求失败:", error);
      throw error;
    }
  }

  // 更新 token（如果需要动态更新）
  updateToken(token: string): void {
    this.token = token;
    console.log("Token 已更新:", this.token);
  }
}

// 创建默认实例
const request = new RequestTool();

export default request;

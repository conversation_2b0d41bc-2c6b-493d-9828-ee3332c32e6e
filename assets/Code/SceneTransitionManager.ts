import { director } from 'cc';

/**
 * 场景切换管理器
 * 用于安全地处理场景切换，确保资源正确清理
 */
export class SceneTransitionManager {
	private static instance: SceneTransitionManager | null = null;
	private isTransitioning: boolean = false;
	private transitionCallbacks: (() => void)[] = [];

	/**
	 * 获取单例实例
	 */
	public static getInstance(): SceneTransitionManager {
		if (!SceneTransitionManager.instance) {
			SceneTransitionManager.instance = new SceneTransitionManager();
		}
		return SceneTransitionManager.instance;
	}

	/**
	 * 私有构造函数
	 */
	private constructor() {
		console.log('场景切换管理器已初始化');
	}

	/**
	 * 安全地切换场景
	 * @param sceneName 目标场景名称
	 * @param onProgress 加载进度回调
	 * @param onComplete 完成回调
	 */
	public switchScene(
		sceneName: string,
		onProgress?: (completedCount: number, totalCount: number) => void,
		onComplete?: (error?: any) => void
	): void {
		if (this.isTransitioning) {
			console.warn(`场景切换进行中，忽略切换到 ${sceneName} 的请求`);
			return;
		}

		this.isTransitioning = true;
		console.log(`开始切换到场景: ${sceneName}`);

		// 执行切换前的清理回调
		this.executeTransitionCallbacks();

		// 预加载场景
		director.preloadScene(
			sceneName,
			(completedCount: number, totalCount: number, item: any) => {
				if (onProgress) {
					onProgress(completedCount, totalCount);
				}
				console.log(`预加载 ${sceneName} 进度: ${completedCount}/${totalCount}`);
			},
			(error: any) => {
				if (error) {
					console.error(`预加载场景 ${sceneName} 失败:`, error);
					// 预加载失败，尝试直接加载
					this.loadSceneDirect(sceneName, onComplete);
				} else {
					console.log(`场景 ${sceneName} 预加载完成，开始切换`);
					// 预加载成功，执行场景切换
					this.loadSceneDirect(sceneName, onComplete);
				}
			}
		);
	}

	/**
	 * 直接加载场景
	 * @param sceneName 场景名称
	 * @param onComplete 完成回调
	 */
	private loadSceneDirect(sceneName: string, onComplete?: (error?: any) => void): void {
		director.loadScene(sceneName, (error: any) => {
			this.isTransitioning = false;
			
			if (error) {
				console.error(`切换到场景 ${sceneName} 失败:`, error);
			} else {
				console.log(`成功切换到场景: ${sceneName}`);
			}

			if (onComplete) {
				onComplete(error);
			}
		});
	}

	/**
	 * 注册场景切换前的清理回调
	 * @param callback 清理回调函数
	 */
	public registerTransitionCallback(callback: () => void): void {
		this.transitionCallbacks.push(callback);
		console.log(`注册场景切换回调，当前回调数量: ${this.transitionCallbacks.length}`);
	}

	/**
	 * 移除场景切换回调
	 * @param callback 要移除的回调函数
	 */
	public unregisterTransitionCallback(callback: () => void): void {
		const index = this.transitionCallbacks.indexOf(callback);
		if (index > -1) {
			this.transitionCallbacks.splice(index, 1);
			console.log(`移除场景切换回调，当前回调数量: ${this.transitionCallbacks.length}`);
		}
	}

	/**
	 * 执行所有场景切换回调
	 */
	private executeTransitionCallbacks(): void {
		console.log(`执行 ${this.transitionCallbacks.length} 个场景切换回调`);
		
		this.transitionCallbacks.forEach((callback, index) => {
			try {
				callback();
				console.log(`场景切换回调 ${index + 1} 执行成功`);
			} catch (error) {
				console.error(`场景切换回调 ${index + 1} 执行失败:`, error);
			}
		});

		// 清空回调列表
		this.transitionCallbacks = [];
	}

	/**
	 * 检查是否正在切换场景
	 */
	public isSceneTransitioning(): boolean {
		return this.isTransitioning;
	}

	/**
	 * 强制重置切换状态（紧急情况使用）
	 */
	public forceResetTransitionState(): void {
		console.warn('强制重置场景切换状态');
		this.isTransitioning = false;
	}

	/**
	 * 获取当前注册的回调数量
	 */
	public getCallbackCount(): number {
		return this.transitionCallbacks.length;
	}

	/**
	 * 清理所有回调（通常在应用退出时调用）
	 */
	public clearAllCallbacks(): void {
		console.log(`清理 ${this.transitionCallbacks.length} 个场景切换回调`);
		this.transitionCallbacks = [];
	}

	/**
	 * 销毁管理器
	 */
	public destroy(): void {
		this.clearAllCallbacks();
		this.isTransitioning = false;
		SceneTransitionManager.instance = null;
		console.log('场景切换管理器已销毁');
	}
}

/**
 * 获取场景切换管理器实例
 */
export function getSceneTransitionManager(): SceneTransitionManager {
	return SceneTransitionManager.getInstance();
}

/**
 * 安全切换场景的便捷方法
 * @param sceneName 目标场景名称
 * @param onProgress 进度回调
 * @param onComplete 完成回调
 */
export function switchSceneSafely(
	sceneName: string,
	onProgress?: (completedCount: number, totalCount: number) => void,
	onComplete?: (error?: any) => void
): void {
	const manager = getSceneTransitionManager();
	manager.switchScene(sceneName, onProgress, onComplete);
}

/**
 * 注册组件的场景切换清理回调
 * 通常在组件的start方法中调用
 * @param cleanupCallback 清理回调函数
 */
export function registerSceneCleanup(cleanupCallback: () => void): void {
	const manager = getSceneTransitionManager();
	manager.registerTransitionCallback(cleanupCallback);
}

/**
 * 移除组件的场景切换清理回调
 * 通常在组件的onDestroy方法中调用
 * @param cleanupCallback 要移除的清理回调函数
 */
export function unregisterSceneCleanup(cleanupCallback: () => void): void {
	const manager = getSceneTransitionManager();
	manager.unregisterTransitionCallback(cleanupCallback);
}

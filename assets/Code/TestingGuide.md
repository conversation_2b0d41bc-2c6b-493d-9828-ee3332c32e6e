# 场景切换问题修复测试指南

## 测试目标

验证场景快速切换时定时器清理和请求重复问题已得到解决。

## 测试环境准备

1. **开启网络监控**：在浏览器开发者工具中打开Network面板
2. **开启控制台**：查看日志输出，确认清理过程
3. **准备测试场景**：确保Info场景和JiangChi场景都可以正常访问

## 测试用例

### 1. 基础功能测试

#### 1.1 单场景定时器测试
- **步骤**：
  1. 进入Info场景
  2. 等待5-10秒观察定时器执行
  3. 检查网络请求是否正常
- **预期结果**：
  - 定时器按预期间隔执行
  - 网络请求正常，无重复请求
  - 控制台显示定时器启动日志

#### 1.2 单场景清理测试
- **步骤**：
  1. 在Info场景停留一段时间
  2. 切换到JiangChi场景
  3. 观察控制台日志
- **预期结果**：
  - 显示"场景切换前清理Info组件资源"
  - 显示"Info组件已销毁，所有管理器已清理"
  - 网络请求停止

### 2. 快速切换测试

#### 2.1 快速双向切换
- **步骤**：
  1. 从Info场景快速切换到JiangChi场景（间隔1秒内）
  2. 立即从JiangChi场景切换回Info场景
  3. 重复此操作5-10次
- **预期结果**：
  - 每次切换都有清理日志
  - 网络面板中无重复请求
  - 无错误日志

#### 2.2 连续快速切换
- **步骤**：
  1. 连续快速点击场景切换按钮（每0.5秒一次）
  2. 持续10-15秒
  3. 观察网络请求和控制台
- **预期结果**：
  - 场景切换管理器阻止重复切换
  - 控制台显示"场景切换进行中，忽略切换请求"
  - 无异常错误

### 3. 网络请求测试

#### 3.1 请求防重复测试
- **步骤**：
  1. 进入JiangChi场景
  2. 在网络较慢的情况下快速切换场景
  3. 观察网络请求状态
- **预期结果**：
  - 同一时间只有一个奖池信息请求
  - 组件销毁后的响应被忽略
  - 控制台显示"跳过奖池信息请求"或"组件已销毁，忽略响应"

#### 3.2 定时器请求测试
- **步骤**：
  1. 进入Info场景，等待定时器启动
  2. 观察游戏数据请求和中奖查询请求
  3. 快速切换场景
- **预期结果**：
  - 定时器请求按间隔正常执行
  - 场景切换后请求立即停止
  - 无残留的定时器请求

### 4. 内存泄漏测试

#### 4.1 长时间运行测试
- **步骤**：
  1. 在两个场景间切换20-30次
  2. 使用浏览器性能工具监控内存使用
  3. 观察内存是否持续增长
- **预期结果**：
  - 内存使用保持稳定
  - 垃圾回收后内存能正常释放
  - 无明显内存泄漏

#### 4.2 定时器清理验证
- **步骤**：
  1. 在场景中停留一段时间
  2. 切换场景
  3. 使用TimerManager的调试方法检查状态
- **预期结果**：
  - 切换前有活跃定时器
  - 切换后定时器被清理
  - 管理器状态正常

## 测试检查点

### 控制台日志检查
应该看到以下关键日志：
```
✓ "所有管理器初始化完成"
✓ "奖池信息定时器已启动"
✓ "游戏数据刷新定时器已启动"
✓ "场景切换前清理组件资源"
✓ "组件已销毁，所有管理器已清理"
✓ "场景切换进行中，忽略切换请求"（快速切换时）
```

### 网络请求检查
- 请求间隔符合预期（5秒、10秒等）
- 无重复的同类型请求
- 场景切换后请求立即停止

### 错误日志检查
不应该出现以下错误：
```
❌ "Cannot read property of null"
❌ "Timer callback error"
❌ "Component already destroyed"
❌ 重复的API请求错误
```

## 性能监控

### 使用浏览器开发者工具
1. **Performance面板**：录制场景切换过程，检查是否有异常的长时间任务
2. **Memory面板**：监控内存使用，确认无泄漏
3. **Network面板**：验证请求模式正确

### 使用TimerManager调试功能
```typescript
// 在控制台中执行
timerManager.debugPrint();
```

## 问题排查

### 如果仍有重复请求
1. 检查是否有多个组件实例
2. 确认TimerManager是否正确初始化
3. 验证场景切换回调是否正确注册

### 如果场景切换失败
1. 检查SceneTransitionManager状态
2. 确认场景名称正确
3. 查看预加载是否成功

### 如果出现内存泄漏
1. 检查onDestroy是否被调用
2. 确认所有管理器都被正确销毁
3. 验证事件监听器是否被移除

## 测试通过标准

- ✅ 无重复网络请求
- ✅ 场景切换流畅，无卡顿
- ✅ 内存使用稳定
- ✅ 控制台无错误日志
- ✅ 定时器正确清理
- ✅ 快速切换被正确处理

通过以上测试，可以确认场景切换时的定时器和请求重复问题已得到彻底解决。

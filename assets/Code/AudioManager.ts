import { AudioSource, Node } from 'cc';
import { MusicStorageUtil } from './Util';

/**
 * 音频类型枚举
 */
export enum AudioType {
	BACKGROUND = 'background',
	WIN_EFFECT = 'winEffect',
	LOSE_EFFECT = 'loseEffect'
}

/**
 * 音频配置接口
 */
export interface AudioConfig {
	audioSource: AudioSource;
	volume: number;
	loop: boolean;
	autoPlay: boolean;
}

/**
 * 音频管理器
 * 统一管理背景音乐和音效的播放、停止、音量控制等功能
 */
export class AudioManager {
	private audioSources: Map<AudioType, AudioSource> = new Map();
	private audioConfigs: Map<AudioType, AudioConfig> = new Map();
	private isMusicEnabled: boolean = true;
	private isDestroyed: boolean = false;
	private masterVolume: number = 1.0;

	/**
	 * 构造函数
	 */
	constructor() {
		// 从本地存储获取音乐开关状态
		this.isMusicEnabled = MusicStorageUtil.getMusicEnabled();
		console.log('音频管理器已初始化，音乐状态:', this.isMusicEnabled);
	}

	/**
	 * 注册音频源
	 * @param type 音频类型
	 * @param node 包含AudioSource组件的节点
	 * @param volume 音量（0-1）
	 * @param loop 是否循环
	 * @param autoPlay 是否自动播放
	 */
	public registerAudioSource(
		type: AudioType,
		node: Node,
		volume: number = 1.0,
		loop: boolean = false,
		autoPlay: boolean = false
	): boolean {
		if (!node || this.isDestroyed) {
			console.warn(`无法注册音频源 ${type}: 节点不存在或管理器已销毁`);
			return false;
		}

		const audioSource = node.getComponent(AudioSource);
		if (!audioSource) {
			console.warn(`节点上没有找到AudioSource组件: ${type}`);
			return false;
		}

		const config: AudioConfig = {
			audioSource,
			volume,
			loop,
			autoPlay
		};

		this.audioSources.set(type, audioSource);
		this.audioConfigs.set(type, config);

		// 设置音频源属性
		audioSource.loop = loop;
		audioSource.volume = volume * this.masterVolume;

		console.log(`音频源 ${type} 注册成功`);
		return true;
	}

	/**
	 * 从节点自动注册音频源
	 * @param type 音频类型
	 * @param node 包含AudioSource组件的节点
	 * @param volume 音量（0-1）
	 * @param loop 是否循环
	 */
	public registerAudioFromNode(
		type: AudioType,
		node: Node | null,
		volume: number = 1.0,
		loop: boolean = false
	): boolean {
		if (!node) {
			console.warn(`无法注册音频源 ${type}: 节点为空`);
			return false;
		}

		return this.registerAudioSource(type, node, volume, loop, false);
	}

	/**
	 * 播放音频
	 * @param type 音频类型
	 * @param forcePlay 是否强制播放（忽略音乐开关状态）
	 */
	public playAudio(type: AudioType, forcePlay: boolean = false): boolean {
		if (this.isDestroyed) {
			console.warn('音频管理器已销毁');
			return false;
		}

		// 检查音乐开关状态
		if (!forcePlay && !this.isMusicEnabled) {
			console.log(`音乐已关闭，跳过播放 ${type}`);
			return false;
		}

		const audioSource = this.audioSources.get(type);
		if (!audioSource) {
			console.warn(`音频源 ${type} 未注册`);
			return false;
		}

		try {
			audioSource.play();
			console.log(`播放音频: ${type}`);
			return true;
		} catch (error) {
			console.error(`播放音频失败 ${type}:`, error);
			return false;
		}
	}

	/**
	 * 停止音频
	 * @param type 音频类型
	 */
	public stopAudio(type: AudioType): boolean {
		if (this.isDestroyed) {
			console.warn('音频管理器已销毁');
			return false;
		}

		const audioSource = this.audioSources.get(type);
		if (!audioSource) {
			console.warn(`音频源 ${type} 未注册`);
			return false;
		}

		try {
			audioSource.stop();
			console.log(`停止音频: ${type}`);
			return true;
		} catch (error) {
			console.error(`停止音频失败 ${type}:`, error);
			return false;
		}
	}

	/**
	 * 暂停音频
	 * @param type 音频类型
	 */
	public pauseAudio(type: AudioType): boolean {
		if (this.isDestroyed) {
			console.warn('音频管理器已销毁');
			return false;
		}

		const audioSource = this.audioSources.get(type);
		if (!audioSource) {
			console.warn(`音频源 ${type} 未注册`);
			return false;
		}

		try {
			audioSource.pause();
			console.log(`暂停音频: ${type}`);
			return true;
		} catch (error) {
			console.error(`暂停音频失败 ${type}:`, error);
			return false;
		}
	}

	/**
	 * 恢复音频播放
	 * @param type 音频类型
	 */
	public resumeAudio(type: AudioType): boolean {
		if (this.isDestroyed) {
			console.warn('音频管理器已销毁');
			return false;
		}

		const audioSource = this.audioSources.get(type);
		if (!audioSource) {
			console.warn(`音频源 ${type} 未注册`);
			return false;
		}

		try {
			// 如果音乐开关关闭，不恢复播放
			if (!this.isMusicEnabled) {
				console.log(`音乐已关闭，不恢复播放 ${type}`);
				return false;
			}

			audioSource.play();
			console.log(`恢复音频: ${type}`);
			return true;
		} catch (error) {
			console.error(`恢复音频失败 ${type}:`, error);
			return false;
		}
	}

	/**
	 * 设置音频音量
	 * @param type 音频类型
	 * @param volume 音量（0-1）
	 */
	public setVolume(type: AudioType, volume: number): boolean {
		if (this.isDestroyed) {
			console.warn('音频管理器已销毁');
			return false;
		}

		const audioSource = this.audioSources.get(type);
		const config = this.audioConfigs.get(type);

		if (!audioSource || !config) {
			console.warn(`音频源 ${type} 未注册`);
			return false;
		}

		// 更新配置
		config.volume = Math.max(0, Math.min(1, volume));
		// 应用主音量
		audioSource.volume = config.volume * this.masterVolume;

		console.log(`设置音频 ${type} 音量: ${config.volume}`);
		return true;
	}

	/**
	 * 设置主音量
	 * @param volume 主音量（0-1）
	 */
	public setMasterVolume(volume: number): void {
		this.masterVolume = Math.max(0, Math.min(1, volume));

		// 更新所有音频源的音量
		this.audioConfigs.forEach((config, type) => {
			const audioSource = this.audioSources.get(type);
			if (audioSource) {
				audioSource.volume = config.volume * this.masterVolume;
			}
		});

		console.log(`设置主音量: ${this.masterVolume}`);
	}

	/**
	 * 切换音乐开关状态
	 */
	public toggleMusic(): boolean {
		this.isMusicEnabled = !this.isMusicEnabled;

		// 保存状态到本地存储
		MusicStorageUtil.saveMusicEnabled(this.isMusicEnabled);

		if (this.isMusicEnabled) {
			console.log('音乐已开启');
		} else {
			// 停止所有音频
			this.stopAllAudio();
			console.log('音乐已关闭');
		}

		return this.isMusicEnabled;
	}

	/**
	 * 设置音乐开关状态
	 * @param enabled 是否开启音乐
	 */
	public setMusicEnabled(enabled: boolean): void {
		this.isMusicEnabled = enabled;
		MusicStorageUtil.saveMusicEnabled(enabled);

		if (!enabled) {
			this.stopAllAudio();
		}

		console.log(`音乐状态设置为: ${enabled ? '开启' : '关闭'}`);
	}

	/**
	 * 获取音乐开关状态
	 */
	public getMusicEnabled(): boolean {
		return this.isMusicEnabled;
	}

	/**
	 * 停止所有音频
	 */
	public stopAllAudio(): void {
		this.audioSources.forEach((audioSource, type) => {
			try {
				audioSource.stop();
			} catch (error) {
				console.error(`停止音频失败 ${type}:`, error);
			}
		});
		console.log('所有音频已停止');
	}

	/**
	 * 播放背景音乐
	 */
	public playBackgroundMusic(): boolean {
		return this.playAudio(AudioType.BACKGROUND);
	}

	/**
	 * 停止背景音乐
	 */
	public stopBackgroundMusic(): boolean {
		return this.stopAudio(AudioType.BACKGROUND);
	}

	/**
	 * 播放中奖音效
	 */
	public playWinEffect(): boolean {
		return this.playAudio(AudioType.WIN_EFFECT);
	}

	/**
	 * 播放失败音效
	 */
	public playLoseEffect(): boolean {
		return this.playAudio(AudioType.LOSE_EFFECT);
	}

	/**
	 * 检查音频是否正在播放
	 * @param type 音频类型
	 */
	public isPlaying(type: AudioType): boolean {
		const audioSource = this.audioSources.get(type);
		return audioSource ? audioSource.playing : false;
	}

	/**
	 * 获取注册的音频源数量
	 */
	public getAudioSourceCount(): number {
		return this.audioSources.size;
	}

	/**
	 * 销毁音频管理器
	 */
	public destroy(): void {
		this.isDestroyed = true;
		this.stopAllAudio();
		this.audioSources.clear();
		this.audioConfigs.clear();
		console.log('音频管理器已销毁');
	}

	/**
	 * 检查是否已销毁
	 */
	public getIsDestroyed(): boolean {
		return this.isDestroyed;
	}
}

/**
 * 创建音频管理器实例
 */
export function createAudioManager(): AudioManager {
	return new AudioManager();
}

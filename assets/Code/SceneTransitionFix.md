# 场景切换定时器问题修复总结

## 问题描述

在快速切换场景时，定时器可能没有正确清理，导致同一个请求被多次发送的问题。主要表现为：

1. **定时器重复创建**：场景切换时，新场景的定时器启动，但旧场景的定时器可能还在运行
2. **请求重复发送**：多个定时器同时调用同一个API接口
3. **内存泄漏**：未正确清理的定时器继续占用内存
4. **组件状态混乱**：已销毁的组件仍在执行回调

## 修复方案

### 1. 添加请求状态管理

为每个组件添加请求状态标记，防止重复请求：

```typescript
// 请求状态管理
private isRequestingPrizePool: boolean = false;
private isDestroyed: boolean = false;
private isRequestingGameData: boolean = false;
private isRequestingDrops: boolean = false;
```

### 2. 在请求方法中添加状态检查

```typescript
async fetchPrizePoolInfo() {
    // 防止重复请求和组件销毁后的请求
    if (this.isRequestingPrizePool || this.isDestroyed) {
        console.log('跳过奖池信息请求：', {
            isRequesting: this.isRequestingPrizePool,
            isDestroyed: this.isDestroyed
        });
        return;
    }

    this.isRequestingPrizePool = true;
    
    try {
        const response = await request.get('/app-api/ppx/game/prize-pool/info');
        
        // 再次检查组件是否已销毁
        if (this.isDestroyed) {
            console.log('组件已销毁，忽略奖池信息响应');
            return;
        }
        
        // 处理响应...
    } catch (error) {
        console.error('获取奖池信息失败:', error);
    } finally {
        // 重置请求状态
        this.isRequestingPrizePool = false;
    }
}
```

### 3. 增强定时器管理

在定时器启动时添加重复检查和状态验证：

```typescript
private startFetchPrizePoolTimer() {
    if (this.timerManager && !this.isDestroyed) {
        // 先清理可能存在的定时器
        this.timerManager.clearInterval('fetchPrizePool');
        
        // 创建新的定时器
        this.timerManager.setInterval(
            'fetchPrizePool',
            () => {
                // 在定时器回调中再次检查组件状态
                if (!this.isDestroyed) {
                    this.fetchPrizePoolInfo();
                }
            },
            5000
        );
        
        console.log('奖池信息定时器已启动');
    }
}
```

### 4. 创建场景切换管理器

新增 `SceneTransitionManager` 来统一管理场景切换：

```typescript
export class SceneTransitionManager {
    private static instance: SceneTransitionManager | null = null;
    private isTransitioning: boolean = false;
    private transitionCallbacks: (() => void)[] = [];

    // 安全地切换场景
    public switchScene(sceneName: string, onProgress?, onComplete?) {
        if (this.isTransitioning) {
            console.warn(`场景切换进行中，忽略切换到 ${sceneName} 的请求`);
            return;
        }

        this.isTransitioning = true;
        
        // 执行切换前的清理回调
        this.executeTransitionCallbacks();
        
        // 预加载并切换场景...
    }
}
```

### 5. 注册场景切换清理回调

在组件初始化时注册清理回调：

```typescript
// 注册场景切换清理回调
this.sceneCleanupCallback = () => {
    console.log('场景切换前清理组件资源');
    this.cleanupBeforeSceneTransition();
};
registerSceneCleanup(this.sceneCleanupCallback);
```

### 6. 立即销毁标记

在组件销毁和场景切换时立即设置销毁标记：

```typescript
onDestroy() {
    // 立即标记为已销毁，防止后续操作
    this.isDestroyed = true;
    
    // 清理资源...
}
```

## 修复的文件

### Info.ts
- 添加了请求状态管理
- 增强了 `getGameData` 和 `checkDrops` 方法的状态检查
- 改进了定时器启动逻辑
- 集成了场景切换管理器

### JiangChi.ts
- 添加了请求状态管理
- 增强了 `fetchPrizePoolInfo` 方法的状态检查
- 改进了定时器启动逻辑
- 集成了场景切换管理器

### 新增文件
- `SceneTransitionManager.ts` - 场景切换管理器

## 修复效果

1. **防止重复请求**：通过状态标记确保同一时间只有一个请求在进行
2. **安全的场景切换**：场景切换前自动清理所有资源
3. **内存泄漏防护**：确保定时器和回调正确清理
4. **状态一致性**：组件销毁后不再执行任何操作

## 使用建议

1. **场景切换**：使用 `switchSceneSafely()` 替代 `director.loadScene()`
2. **定时器管理**：始终使用 `TimerManager` 而不是原生定时器
3. **状态检查**：在异步操作中始终检查组件状态
4. **清理回调**：为需要清理的组件注册场景切换回调

## 测试验证

建议进行以下测试：

1. **快速场景切换测试**：快速在Info和JiangChi场景间切换
2. **网络请求监控**：观察是否还有重复请求
3. **内存使用监控**：检查是否存在内存泄漏
4. **定时器状态检查**：使用管理器的调试方法检查定时器状态

通过这些修复，场景切换时的定时器问题应该得到彻底解决。

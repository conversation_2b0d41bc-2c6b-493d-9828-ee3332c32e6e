import { _decorator, Component, Label } from 'cc';
const { ccclass, property } = _decorator;
import request from './request';
import {
	NumberAnimationUtil,
	NumberAnimationManager,
} from './NumberAnimationUtil';
import { TimerManager, createTimerManager } from './TimerManager';
import {
	switchSceneSafely,
	registerSceneCleanup,
	unregisterSceneCleanup,
} from './SceneTransitionManager';

// 胖胖熊级别统计接口
interface PandaLevelStat {
	/**
	 * 数量
	 */
	count?: number;
	/**
	 * 每日产出
	 */
	dailyOutputPerPanda?: number;
	/**
	 * 级别
	 */
	level?: number;
	/**
	 * 今日产出
	 */
	totalDailyOutput?: number;
	/**
	 * 单个熊猫估值
	 */
	singlePandaEstimatedValue?: number;
}

@ccclass('JiangChi')
export class JiangChi extends Component {
	// 绑定Label组件
	@property(Label)
	// 奖池总储备
	totalPoolPointsLabel: Label = null!;

	@property(Label)
	// 我的胖胖熊数量
	myPandaCountLabel: Label = null!;

	@property(Label)
	// 今日产出
	todayTotalOutputLabel: Label = null!;

	// 胖胖熊级别数据相关Label
	//   @property(Label)
	// 级别
	//   levelLabel: Label = null!;
	@property(Label)
	// 数量
	countLabel: Label = null!;

	@property(Label)
	// 每日产出
	dailyOutputPerPandaLabel: Label = null!;
	// @property(Label)
	// 今日产出
	// totalDailyOutputLabel: Label = null!;

	@property(Label)
	// 估值
	totalEstimatedValueLabel: Label = null!;

	@property(Label)
	// 法力值
	manaLabel: Label = null!;

	// 数据存储
	private pandaLevelStats: PandaLevelStat[] = [];
	private currentIndex: number = 0;

	// 定时器相关（已迁移到TimerManager）
	// private fetchPrizePoolTimer: any = null; // 获取奖池信息定时器引用

	// 数字动画工具
	private numberAnimation: NumberAnimationUtil | null = null;
	private animationManager: NumberAnimationManager | null = null;

	// 管理器实例
	private timerManager: TimerManager | null = null;

	// 请求状态管理
	private isRequestingPrizePool: boolean = false;
	private isDestroyed: boolean = false;

	// 场景切换清理回调
	private sceneCleanupCallback: (() => void) | null = null;

	Jump() {
		// 使用场景切换管理器安全切换到C1场景
		switchSceneSafely(
			'C1',
			(completedCount: number, totalCount: number) => {
				console.log(`切换到C1场景进度: ${completedCount}/${totalCount}`);
			},
			(error: any) => {
				if (error) {
					console.error('切换到C1场景失败:', error);
				} else {
					console.log('成功切换到C1场景');
				}
			}
		);
	}

	start() {
		// 初始化管理器
		this.initializeManagers();

		// 初始化数字动画工具
		if (this.totalPoolPointsLabel) {
			this.numberAnimation = new NumberAnimationUtil(
				this.totalPoolPointsLabel,
				2
			);
		}

		// 组件启动时获取数据
		this.fetchPrizePoolInfo();

		// 启动定时器，每隔5秒调用一次 fetchPrizePoolInfo
		this.startFetchPrizePoolTimer();

		// 初始化Spine动画
	}

	/**
	 * 初始化管理器
	 */
	private initializeManagers(): void {
		// 初始化定时器管理器
		this.timerManager = createTimerManager();

		// 初始化数字动画管理器
		this.animationManager = new NumberAnimationManager();

		// 创建数字动画实例
		if (this.animationManager && this.totalPoolPointsLabel) {
			this.animationManager.createAnimation(
				'totalPoolPoints',
				this.totalPoolPointsLabel,
				2
			);
		}

		// 注册场景切换清理回调
		this.sceneCleanupCallback = () => {
			console.log('场景切换前清理JiangChi组件资源');
			this.cleanupBeforeSceneTransition();
		};
		registerSceneCleanup(this.sceneCleanupCallback);

		console.log('JiangChi管理器初始化完成');
	}

	/**
	 * 场景切换前的清理工作
	 */
	private cleanupBeforeSceneTransition(): void {
		// 立即标记为已销毁
		this.isDestroyed = true;

		// 停止定时器
		this.stopFetchPrizePoolTimer();

		console.log('JiangChi组件场景切换前清理完成');
	}

	update(deltaTime: number) {
		// 更新数字动画
		if (this.numberAnimation) {
			this.numberAnimation.update(deltaTime);
		}

		// 更新动画管理器中的所有动画
		if (this.animationManager) {
			this.animationManager.updateAll(deltaTime);
		}
	}

	/**
	 * 上一个按钮点击事件
	 * 等后续绑定左右切换按钮再使用
	 */
	private onPrevClick() {
		if (this.pandaLevelStats.length > 0) {
			this.currentIndex =
				(this.currentIndex - 1 + this.pandaLevelStats.length) %
				this.pandaLevelStats.length;
			this.updatePandaLevelDisplay();
		}
	}

	/**
	 * 下一个按钮点击事件
	 * 等后续绑定左右切换按钮再使用
	 */
	private onNextClick() {
		if (this.pandaLevelStats.length > 0) {
			this.currentIndex = (this.currentIndex + 1) % this.pandaLevelStats.length;
			this.updatePandaLevelDisplay();
		}
	}

	/**
	 * 更新胖胖熊级别数据显示
	 */
	private updatePandaLevelDisplay() {
		if (
			this.pandaLevelStats.length === 0 ||
			this.currentIndex >= this.pandaLevelStats.length
		) {
			return;
		}

		const currentData = this.pandaLevelStats[this.currentIndex];

		// this.levelLabel.string = currentData.level || 0;

		if (this.countLabel) {
			this.countLabel.string = (currentData.count || 0).toString();
		}

		// this.dailyOutputPerPandaLabel.string = currentData.dailyOutputPerPanda || 0;

		if (this.dailyOutputPerPandaLabel) {
			this.dailyOutputPerPandaLabel.string = `${
				currentData.dailyOutputPerPanda || 0
			}/每只`;
		}

		if (this.totalEstimatedValueLabel) {
			this.totalEstimatedValueLabel.string = `${
				currentData.singlePandaEstimatedValue || 0
			}积分`;
		}
	}

	/**
	 * 获取奖池信息
	 */
	async fetchPrizePoolInfo() {
		// 防止重复请求和组件销毁后的请求
		if (this.isRequestingPrizePool || this.isDestroyed) {
			console.log('跳过奖池信息请求：', {
				isRequesting: this.isRequestingPrizePool,
				isDestroyed: this.isDestroyed,
			});
			return;
		}

		this.isRequestingPrizePool = true;

		try {
			const response = await request.get('/app-api/ppx/game/prize-pool/info');

			// 再次检查组件是否已销毁
			if (this.isDestroyed) {
				console.log('组件已销毁，忽略奖池信息响应');
				return;
			}

			if (response && response.data) {
				const data = response.data;

				// 基础数据赋值 - 使用数字动画效果显示总储备
				const totalPoolPoints = parseFloat(data.totalPoolPoints) || 0;

				// 使用旧的动画工具
				if (this.numberAnimation) {
					this.numberAnimation.startAnimation(totalPoolPoints);
				}

				// 使用新的动画管理器
				if (this.animationManager) {
					const animation =
						this.animationManager.getAnimation('totalPoolPoints');
					if (animation) {
						animation.startAnimation(totalPoolPoints);
					}
				}
				if (this.myPandaCountLabel) {
					this.myPandaCountLabel.string = data.myPandaCount || '';
				}
				if (this.todayTotalOutputLabel) {
					this.todayTotalOutputLabel.string = data.todayTotalOutput || '';
				}

				// 处理法力值
				if (data.mana !== undefined && this.manaLabel) {
					this.manaLabel.string = data.mana || '';
				}

				// 处理胖胖熊级别数据
				if (data.pandaLevelStats && Array.isArray(data.pandaLevelStats)) {
					this.pandaLevelStats = data.pandaLevelStats;
					this.currentIndex = 0;
					this.updatePandaLevelDisplay();
				}
			}
		} catch (error) {
			console.error('获取奖池信息失败:', error);
		} finally {
			// 重置请求状态
			this.isRequestingPrizePool = false;
		}
	}

	/**
	 * 手动刷新数据（可在需要时调用）
	 */
	public refreshData() {
		this.fetchPrizePoolInfo();
	}

	/**
	 * 获取当前显示的胖胖熊级别数据
	 * 备用方法
	 */
	public getCurrentPandaLevelData(): PandaLevelStat | null {
		if (
			this.pandaLevelStats.length > 0 &&
			this.currentIndex < this.pandaLevelStats.length
		) {
			return this.pandaLevelStats[this.currentIndex];
		}
		return null;
	}

	/**
	 * 启动获取奖池信息定时器
	 */
	private startFetchPrizePoolTimer() {
		if (this.timerManager && !this.isDestroyed) {
			// 先清理可能存在的定时器
			this.timerManager.clearInterval('fetchPrizePool');

			// 创建新的定时器
			this.timerManager.setInterval(
				'fetchPrizePool',
				() => {
					// 在定时器回调中再次检查组件状态
					if (!this.isDestroyed) {
						this.fetchPrizePoolInfo();
					}
				},
				5000
			); // 5秒 = 5000毫秒

			console.log('奖池信息定时器已启动');
		}
	}

	/**
	 * 停止获取奖池信息定时器
	 */
	private stopFetchPrizePoolTimer() {
		if (this.timerManager) {
			this.timerManager.clearInterval('fetchPrizePool');
		}
	}

	/**
	 * 组件销毁时清理定时器
	 */
	onDestroy() {
		// 立即标记为已销毁，防止后续操作
		this.isDestroyed = true;

		console.log('JiangChi组件开始销毁...');

		// 停止定时器
		this.stopFetchPrizePoolTimer();

		// 销毁管理器
		if (this.timerManager) {
			this.timerManager.destroy();
			this.timerManager = null;
		}

		if (this.animationManager) {
			this.animationManager.clear();
			this.animationManager = null;
		}

		// 重置状态
		this.isRequestingPrizePool = false;

		// 移除场景切换清理回调
		if (this.sceneCleanupCallback) {
			unregisterSceneCleanup(this.sceneCleanupCallback);
			this.sceneCleanupCallback = null;
		}

		console.log('JiangChi组件已销毁，所有管理器已清理');
	}
}

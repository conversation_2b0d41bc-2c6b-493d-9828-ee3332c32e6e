import { sp } from 'cc';

/**
 * 动画状态枚举
 */
export enum AnimationState {
	ATTACK = 'attack',
	SLEEP = 'shuijiao',
	SLEEP_START = 'shuijiao_1',
	END = 'end',
	WIN = 'dajiang',
	LOSE = 'zhadan'
}

/**
 * 动画配置接口
 */
export interface AnimationConfig {
	name: string;
	loop: boolean;
	onComplete?: () => void;
	onStart?: () => void;
}

/**
 * Spine动画管理器
 * 统一管理Spine动画的播放、监听和状态控制
 */
export class AnimationManager {
	private spine: sp.Skeleton | null = null;
	private currentAnimation: string = '';
	private isDestroyed: boolean = false;
	private animationQueue: AnimationConfig[] = []; // 动画队列
	private isPlaying: boolean = false;

	/**
	 * 构造函数
	 * @param spine Spine动画组件
	 */
	constructor(spine: sp.Skeleton) {
		this.spine = spine;
		this.setupCompleteListener();
	}

	/**
	 * 设置动画完成监听器
	 */
	private setupCompleteListener(): void {
		if (!this.spine) return;

		this.spine.setCompleteListener((trackEntry) => {
			if (this.isDestroyed) return;

			const animationName = trackEntry.animation.name;
			console.log(`动画 ${animationName} 播放完成`);

			// 处理动画队列中的下一个动画
			this.processNextAnimation();
		});
	}

	/**
	 * 处理动画队列中的下一个动画
	 */
	private processNextAnimation(): void {
		if (this.animationQueue.length > 0) {
			const nextAnimation = this.animationQueue.shift();
			if (nextAnimation) {
				this.playAnimationInternal(nextAnimation);
			}
		} else {
			this.isPlaying = false;
		}
	}

	/**
	 * 内部播放动画方法
	 */
	private playAnimationInternal(config: AnimationConfig): void {
		if (!this.spine || this.isDestroyed) return;

		this.currentAnimation = config.name;
		this.isPlaying = true;

		// 调用开始回调
		if (config.onStart) {
			config.onStart();
		}

		// 播放动画
		this.spine.setAnimation(0, config.name, config.loop);

		// 如果是循环动画，立即调用完成回调
		if (config.loop && config.onComplete) {
			config.onComplete();
		}

		console.log(`播放动画: ${config.name}, 循环: ${config.loop}`);
	}

	/**
	 * 播放单个动画
	 * @param name 动画名称
	 * @param loop 是否循环
	 * @param onComplete 完成回调
	 * @param onStart 开始回调
	 */
	public playAnimation(
		name: string,
		loop: boolean = false,
		onComplete?: () => void,
		onStart?: () => void
	): void {
		if (!this.spine || this.isDestroyed) {
			console.warn('动画管理器已销毁或Spine组件不存在');
			return;
		}

		const config: AnimationConfig = {
			name,
			loop,
			onComplete,
			onStart
		};

		// 清空队列，立即播放新动画
		this.animationQueue = [];
		this.playAnimationInternal(config);
	}

	/**
	 * 播放动画序列
	 * @param animations 动画配置数组
	 */
	public playAnimationSequence(animations: AnimationConfig[]): void {
		if (!this.spine || this.isDestroyed || animations.length === 0) {
			console.warn('动画管理器已销毁、Spine组件不存在或动画序列为空');
			return;
		}

		// 清空当前队列
		this.animationQueue = [];

		// 播放第一个动画
		const firstAnimation = animations[0];
		this.playAnimationInternal(firstAnimation);

		// 将剩余动画加入队列
		if (animations.length > 1) {
			this.animationQueue = animations.slice(1);
		}
	}

	/**
	 * 播放攻击动画
	 */
	public playAttackAnimation(): void {
		this.playAnimation(AnimationState.ATTACK, true);
	}

	/**
	 * 播放睡觉动画序列（先播放end，再播放shuijiao循环）
	 */
	public playSleepAnimation(): void {
		const animations: AnimationConfig[] = [
			{
				name: AnimationState.END,
				loop: false
			},
			{
				name: AnimationState.SLEEP,
				loop: true
			}
		];
		this.playAnimationSequence(animations);
	}

	/**
	 * 播放唤醒动画序列（先播放shuijiao_1，再播放attack循环）
	 */
	public playWakeUpAnimation(): void {
		const animations: AnimationConfig[] = [
			{
				name: AnimationState.SLEEP_START,
				loop: false
			},
			{
				name: AnimationState.ATTACK,
				loop: true
			}
		];
		this.playAnimationSequence(animations);
	}

	/**
	 * 播放中奖动画（播放dajiang，完成后播放attack循环）
	 * @param onWinComplete 中奖动画完成回调
	 */
	public playWinAnimation(onWinComplete?: () => void): void {
		const animations: AnimationConfig[] = [
			{
				name: AnimationState.WIN,
				loop: false,
				onComplete: onWinComplete
			},
			{
				name: AnimationState.ATTACK,
				loop: true
			}
		];
		this.playAnimationSequence(animations);
	}

	/**
	 * 播放失败动画（播放zhadan，完成后播放attack循环）
	 * @param onLoseComplete 失败动画完成回调
	 */
	public playLoseAnimation(onLoseComplete?: () => void): void {
		const animations: AnimationConfig[] = [
			{
				name: AnimationState.LOSE,
				loop: false,
				onComplete: onLoseComplete
			},
			{
				name: AnimationState.ATTACK,
				loop: true
			}
		];
		this.playAnimationSequence(animations);
	}

	/**
	 * 停止当前动画
	 */
	public stopAnimation(): void {
		if (!this.spine || this.isDestroyed) return;

		this.animationQueue = [];
		this.isPlaying = false;
		this.currentAnimation = '';
		console.log('动画已停止');
	}

	/**
	 * 获取当前播放的动画名称
	 */
	public getCurrentAnimation(): string {
		return this.currentAnimation;
	}

	/**
	 * 检查是否正在播放动画
	 */
	public isAnimationPlaying(): boolean {
		return this.isPlaying;
	}

	/**
	 * 检查是否正在播放指定动画
	 * @param animationName 动画名称
	 */
	public isPlayingAnimation(animationName: string): boolean {
		return this.currentAnimation === animationName && this.isPlaying;
	}

	/**
	 * 获取Spine组件当前动画信息
	 */
	public getCurrentSpineAnimation(): any {
		if (!this.spine) return null;
		return this.spine.getCurrent(0);
	}

	/**
	 * 销毁动画管理器
	 */
	public destroy(): void {
		this.isDestroyed = true;
		this.animationQueue = [];
		this.isPlaying = false;
		this.currentAnimation = '';

		if (this.spine) {
			this.spine.setCompleteListener(null);
			this.spine = null;
		}

		console.log('动画管理器已销毁');
	}

	/**
	 * 检查是否已销毁
	 */
	public getIsDestroyed(): boolean {
		return this.isDestroyed;
	}
}

/**
 * 创建动画管理器实例
 * @param spine Spine动画组件
 */
export function createAnimationManager(spine: sp.Skeleton): AnimationManager {
	return new AnimationManager(spine);
}

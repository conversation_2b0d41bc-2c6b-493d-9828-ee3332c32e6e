import { Label } from "cc";

/**
 * 数字动画工具类
 * 提供数字从0到目标值的平滑动画效果
 */
export class NumberAnimationUtil {
  private isAnimating: boolean = false;
  private animationTimer: any = null;
  private currentValue: number = 0;
  private targetValue: number = 0;
  private animationDuration: number = 1.0; // 动画持续时间（秒）
  private animationStartTime: number = 0;
  private label: Label = null;
  private onComplete: (() => void) | null = null;
  private decimalPlaces: number = 2; // 小数位数

  /**
   * 构造函数
   * @param label 要显示动画的Label组件
   * @param decimalPlaces 小数位数，默认2位
   */
  constructor(label: Label, decimalPlaces: number = 2) {
    this.label = label;
    this.decimalPlaces = decimalPlaces;
  }

  /**
   * 开始数字动画
   * @param targetValue 目标数值
   * @param duration 动画持续时间（可选，默认1秒）
   * @param onComplete 动画完成回调（可选）
   */
  public startAnimation(
    targetValue: number,
    duration?: number,
    onComplete?: () => void
  ): void {
    this.targetValue = targetValue;
    this.currentValue = 0;
    this.isAnimating = true;
    this.animationStartTime = Date.now();
    this.onComplete = onComplete || null;

    if (duration) {
      this.animationDuration = duration;
    }

    // 立即显示初始值
    this.updateDisplay(0);
  }

  /**
   * 停止动画
   */
  public stopAnimation(): void {
    this.isAnimating = false;
    if (this.animationTimer) {
      clearInterval(this.animationTimer);
      this.animationTimer = null;
    }
    this.onComplete = null;
  }

  /**
   * 更新动画（需要在组件的update方法中调用）
   * @param deltaTime 帧间隔时间
   */
  public update(deltaTime: number): void {
    if (!this.isAnimating) return;

    const currentTime = Date.now();
    const elapsed = (currentTime - this.animationStartTime) / 1000;
    const progress = Math.min(elapsed / this.animationDuration, 1);

    // 使用缓动函数使动画更自然
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);

    const currentDisplayValue =
      this.currentValue + (this.targetValue - this.currentValue) * easeOutQuart;

    this.updateDisplay(currentDisplayValue);

    if (progress >= 1) {
      // 动画完成
      this.isAnimating = false;
      this.updateDisplay(this.targetValue);

      // 调用完成回调
      if (this.onComplete) {
        this.onComplete();
        this.onComplete = null;
      }
    }
  }

  /**
   * 更新显示
   * @param value 要显示的值
   */
  private updateDisplay(value: number): void {
    if (this.label) {
      // 检查是否为整数
      if (Number.isInteger(value)) {
        // 整数显示
        this.label.string = value.toString();
      } else {
        // 有小数时显示指定的小数位数
        this.label.string = value.toFixed(this.decimalPlaces);
      }
    }
  }

  /**
   * 设置动画持续时间
   * @param duration 动画持续时间（秒）
   */
  public setAnimationDuration(duration: number): void {
    this.animationDuration = duration;
  }

  /**
   * 检查是否正在播放动画
   */
  public isAnimationPlaying(): boolean {
    return this.isAnimating;
  }

  /**
   * 设置小数位数
   * @param decimalPlaces 小数位数
   */
  public setDecimalPlaces(decimalPlaces: number): void {
    this.decimalPlaces = decimalPlaces;
  }

  /**
   * 获取当前值
   */
  public getCurrentValue(): number {
    return this.currentValue;
  }

  /**
   * 获取目标值
   */
  public getTargetValue(): number {
    return this.targetValue;
  }

  /**
   * 设置Label组件
   * @param label 新的Label组件
   */
  public setLabel(label: Label): void {
    this.label = label;
  }
}

/**
 * 数字动画管理器
 * 用于管理多个数字动画实例
 */
export class NumberAnimationManager {
  private animations: Map<string, NumberAnimationUtil> = new Map();

  /**
   * 创建数字动画实例
   * @param key 动画实例的唯一标识
   * @param label 要显示动画的Label组件
   * @param decimalPlaces 小数位数，默认2位
   */
  public createAnimation(
    key: string,
    label: Label,
    decimalPlaces: number = 2
  ): NumberAnimationUtil {
    const animation = new NumberAnimationUtil(label, decimalPlaces);
    this.animations.set(key, animation);
    return animation;
  }

  /**
   * 获取动画实例
   * @param key 动画实例的唯一标识
   */
  public getAnimation(key: string): NumberAnimationUtil | undefined {
    return this.animations.get(key);
  }

  /**
   * 移除动画实例
   * @param key 动画实例的唯一标识
   */
  public removeAnimation(key: string): void {
    const animation = this.animations.get(key);
    if (animation) {
      animation.stopAnimation();
      this.animations.delete(key);
    }
  }

  /**
   * 更新所有动画（需要在组件的update方法中调用）
   * @param deltaTime 帧间隔时间
   */
  public updateAll(deltaTime: number): void {
    this.animations.forEach((animation) => {
      animation.update(deltaTime);
    });
  }

  /**
   * 停止所有动画
   */
  public stopAllAnimations(): void {
    this.animations.forEach((animation) => {
      animation.stopAnimation();
    });
  }

  /**
   * 清除所有动画实例
   */
  public clear(): void {
    this.stopAllAnimations();
    this.animations.clear();
  }
}

import {
	_decorator,
	Component,
	Node,
	Label,
	director,
	Sprite,
	assetManager,
	SpriteFrame,
	ImageAsset,
	sp,
	AudioSource,
	Button,
} from 'cc';
import request from './request';
// import { NumberAnimationUtil } from './NumberAnimationUtil'; // 已迁移到管理器中
import { MusicStorageUtil, LocalStorageUtil, NumberFormatUtil } from './Util';
import { TimerManager, createTimerManager } from './TimerManager';
import { AnimationManager, createAnimationManager } from './AnimationManager';
import { AudioManager, AudioType, createAudioManager } from './AudioManager';
import {
	switchSceneSafely,
	registerSceneCleanup,
	unregisterSceneCleanup,
} from './SceneTransitionManager';

const { ccclass, property } = _decorator;

@ccclass('Info')
export class Info extends Component {
	@property(Label)
	Name_Label: Label = null;

	@property(Label)
	Time_Label: Label = null;

	@property(Sprite)
	User_Avatar: Sprite = null; // 用户头像

	@property(Label)
	Parent_Name: Label = null; // 邀请人姓名

	@property(Sprite)
	Parent_Avatar: Sprite = null; // 邀请人头像

	@property(Label)
	Ap_Label: Label = null; // 体力值显示

	@property(Label)
	Mana_Label: Label = null; // 法力值显示

	@property(Label)
	Points_Label: Label = null; // 积分显示

	// @property(Node)
	// Panda_Node: Node = null; // Panda节点

	@property(Node)
	Game_Start_Node: Node = null; // 游戏开始节点

	@property(Node)
	Game_Pause_Node: Node = null; // 游戏暂停节点

	@property(Label)
	Game_Label: Label = null; // 游戏开始或者暂停

	@property(sp.Skeleton)
	Panda_Spine: sp.Skeleton = null; // Panda Spine动画组件

	@property(Node)
	TiLi_Node: Node = null; // 体力值节点

	@property(Node)
	GB_Node: Node = null; // 背景音乐节点

	@property(Node)
	Music_Node: Node = null; // 音乐控制节点

	@property(Label)
	D_Number_Label: Label = null; // 兑换的体力显示

	@property(Label)
	Message_Label: Label = null; // 提示信息

	@property(Node)
	Message_Node: Node = null; // 提示信息节点

	@property(Node)
	Fragment_Tips: Node = null; // 碎片提示组件

	@property(Label)
	fragmentAmountLabel: Label = null; // 碎片数量标签

	@property(Node)
	MyLog_Node: Node = null; // 中奖记录节点

	// 音效相关属性（已迁移到AudioManager）
	// private winAudioSource: AudioSource = null; // 中奖音效
	// private loseAudioSource: AudioSource = null; // 未中奖音效

	// 列表选中相关属性
	private listItems: Node[] = []; // 存储所有item节点
	private selectedIndex: number = 0; // 当前选中的索引
	private bg3SpriteFrame: SpriteFrame = null; // 选中状态的背景图片
	private bg4SpriteFrame: SpriteFrame = null; // 未选中状态的背景图片

	isPaused: boolean = false; // 是否暂停
	// 时间相关字段
	currentGameRemainingTime: number = 0; // 本轮游戏剩余时长（秒）
	totalPlayableTime: number = 0; // 本人体力可玩时长（秒）
	timeLeft: number = 0; // 总剩余时间，单位：秒
	// 定时器引用（已迁移到TimerManager）
	// timer: any = null; // 定时器引用
	// timer1: any = null; // 定时器引用
	// checkDropsTimer: any = null; // 中奖结果查询定时器引用

	userAvatarUrl: string = ''; // 用户头像地址
	parentAvatarUrl: string = ''; // 邀请人头像地址

	backgroundAudioSource: AudioSource = null; // 背景音乐组件

	DHamount: number = 10; // 兑换体力数量

	// 显示中奖记录
	ShowLog() {
		if (this.MyLog_Node) {
			this.MyLog_Node.active = true;
		}
	}

	// 数字动画工具（已迁移到管理器中）
	// private apAnimation: NumberAnimationUtil | null = null;
	// private manaAnimation: NumberAnimationUtil | null = null;
	// private pointsAnimation: NumberAnimationUtil | null = null;

	// 管理器实例
	private timerManager: TimerManager | null = null;
	private animationManager: AnimationManager | null = null;
	private audioManager: AudioManager | null = null;

	// 组件状态管理
	private isDestroyed: boolean = false;
	private isRequestingGameData: boolean = false;
	private isRequestingDrops: boolean = false;

	// 场景切换清理回调
	private sceneCleanupCallback: (() => void) | null = null;

	// 获取游戏数据
	async getGameData(needGameStateCheck: boolean = false) {
		// 防止重复请求和组件销毁后的请求
		if (this.isRequestingGameData || this.isDestroyed) {
			console.log('跳过游戏数据请求：', {
				isRequesting: this.isRequestingGameData,
				isDestroyed: this.isDestroyed,
			});
			return;
		}

		this.isRequestingGameData = true;

		try {
			const { data, code } = await request.get('/app-api/ppx/game/home');

			// 检查组件是否已销毁
			if (this.isDestroyed) {
				console.log('组件已销毁，忽略游戏数据响应');
				return;
			}
			if (code === 200) {
				// 使用数字动画显示数据
				const totalAp = parseFloat(data.totalAp) || 0;
				const totalMana = parseFloat(data.totalMana) || 0;
				const totalEarnedPoints = parseFloat(data.mtc) || 0;
				// 根据active字段设置isPaused状态
				this.isPaused = !data.active; // active为false时表示暂停

				// 获取时间数据
				this.currentGameRemainingTime = data.currentGameRemainingTime || 0;
				this.totalPlayableTime = data.totalPlayableTime || 0;

				// 保存游戏时间数据到本地存储
				LocalStorageUtil.setItem('gameTimeData', {
					leaveTime: Date.now(),
					currentGameRemainingTime: this.currentGameRemainingTime,
				});

				// 如果当前状态是暂停并且currentGameRemainingTime有值，就只复制currentGameRemainingTime
				if (this.isPaused && this.currentGameRemainingTime > 0) {
					this.timeLeft = this.currentGameRemainingTime;
				} else {
					this.timeLeft =
						this.currentGameRemainingTime + this.totalPlayableTime;
				}

				// 开始动画
				// if (this.apAnimation) {
				//   this.apAnimation.startAnimation(totalAp);
				// }
				// if (this.manaAnimation) {
				//   this.manaAnimation.startAnimation(totalMana);
				// }
				// if (this.pointsAnimation) {
				//   this.pointsAnimation.startAnimation(totalEarnedPoints);
				// }

				// 直接设置标签文本
				if (this.Ap_Label) {
					this.Ap_Label.string = NumberFormatUtil.formatNumber(totalAp);
				}
				if (this.Mana_Label) {
					this.Mana_Label.string = NumberFormatUtil.formatNumber(totalMana);
				}
				if (this.Points_Label) {
					this.Points_Label.string =
						NumberFormatUtil.formatNumber(totalEarnedPoints);
				}

				// 更新时间显示
				if (this.Time_Label) {
					this.Time_Label.string = this.formatTime(this.timeLeft);
				}

				// 只有在需要时才执行游戏状态判断
				if (needGameStateCheck) {
					// 根据currentGameRemainingTime判断显示哪个节点
					if (this.currentGameRemainingTime > 0) {
						// 如果还有本轮游戏剩余时间，显示游戏暂停节点
						if (this.Game_Start_Node) this.Game_Start_Node.active = false;
						if (this.Game_Pause_Node) this.Game_Pause_Node.active = true;
						if (this.Game_Label)
							this.Game_Label.string = this.isPaused ? '开始' : '暂停';

						// 播放攻击动画
						if (this.animationManager) {
							this.animationManager.playAttackAnimation();
						}
						// 播放背景音乐
						if (this.audioManager) {
							this.audioManager.playBackgroundMusic();
						}
						// 中奖结果查询
						this.startCheckDropsTimer();
						// 启动倒计时
						this.startTimer();
					} else {
						// 如果没有本轮游戏剩余时间，显示游戏开始节点
						if (this.Game_Start_Node) this.Game_Start_Node.active = true;
						if (this.Game_Pause_Node) this.Game_Pause_Node.active = false;
						if (this.Game_Label) this.Game_Label.string = '开始';
					}

					// 隐藏体力值节点
					if (this.TiLi_Node) this.TiLi_Node.active = false;

					// 初始化碎片提示组件为隐藏状态
					if (this.Fragment_Tips) {
						this.Fragment_Tips.active = false;
					}

					// 音效组件已在AudioManager中管理
				}
			}
		} catch (error) {
			console.error('获取游戏数据失败:', error);
		} finally {
			// 重置请求状态
			this.isRequestingGameData = false;
		}
	}

	async getInfo() {
		try {
			const { data, code } = await request.get(
				'/app-api/friend/code/user-and-parent-info?module=ppx'
			);
			if (code === 200) {
				this.Name_Label.string = data.nickname;
				// 设置用户头像地址
				this.userAvatarUrl = data.avatar || '';
				// 加载并显示用户头像
				// if (this.userAvatarUrl && this.User_Avatar) {
				//   this.loadRemoteImage(this.userAvatarUrl);
				// }

				// 设置邀请人信息
				if (data.parentInfo) {
					this.Parent_Name.string = data.parentInfo.nickname || '';
					this.parentAvatarUrl = data.parentInfo.avatar || '';
					// 加载并显示邀请人头像
					// if (this.parentAvatarUrl && this.Parent_Avatar) {
					//   await this.loadAndDisplayParentAvatar(this.parentAvatarUrl);
					// }
				}
			}
		} catch (error) {
			console.error('失败:', error);
		}
	}

	Jump() {
		// 使用场景切换管理器安全切换到JC场景
		switchSceneSafely(
			'JC',
			(completedCount: number, totalCount: number) => {
				console.log(`切换到JC场景进度: ${completedCount}/${totalCount}`);
			},
			(error: any) => {
				if (error) {
					console.error('切换到JC场景失败:', error);
				} else {
					console.log('成功切换到JC场景');
				}
			}
		);
	}

	// 开始游戏 绑定开始ui组件
	async onStartGame() {
		await this.startGame();
		this.getGameData();
	}

	// 暂停游戏 绑定暂停ui组件
	async onPauseGame() {
		if (this.isPaused) {
			// 当前是暂停状态，需要开始游戏
			await this.startGame();
		} else {
			// 当前是运行状态，需要暂停游戏
			await this.pauseGame();
		}
		this.getGameData();
	}

	// 查询中奖结果
	async checkDrops() {
		// 防止重复请求和组件销毁后的请求
		if (this.isRequestingDrops || this.isDestroyed) {
			console.log('跳过中奖结果查询：', {
				isRequesting: this.isRequestingDrops,
				isDestroyed: this.isDestroyed,
			});
			return;
		}

		this.isRequestingDrops = true;

		try {
			const { data, code } = await request.get('/app-api/ppx/game/drops/check');

			// 检查组件是否已销毁
			if (this.isDestroyed) {
				console.log('组件已销毁，忽略中奖结果响应');
				return;
			}
			if (code === 200) {
				// 处理中奖结果
				if (data.fragmentAmount > 0) {
					// 中奖了，播放dajiang动画
					this.playAnimationWithCallback('dajiang');

					// 播放中奖音效
					if (this.audioManager) {
						this.audioManager.playWinEffect();
					}

					// 显示碎片提示组件
					if (this.Fragment_Tips) {
						this.Fragment_Tips.active = true;
					}
					// 更新碎片数量标签
					if (this.fragmentAmountLabel) {
						this.fragmentAmountLabel.string = data.fragmentAmount;
					}

					// 10秒后自动关闭碎片提示
					if (this.timerManager) {
						this.timerManager.setTimeout(
							'hideFragmentTips',
							() => {
								if (this.Fragment_Tips) {
									this.Fragment_Tips.active = false;
								}
							},
							10000
						);
					}
				} else {
					// 没有中奖，播放zhadan动画并在特定时间点播放音效
					this.playAnimationWithCallback('zhadan');

					// 1秒钟后播放未中奖音效
					if (this.timerManager) {
						this.timerManager.setTimeout(
							'loseEffect',
							() => {
								if (this.audioManager) {
									this.audioManager.playLoseEffect();
								}
							},
							1000
						);
					}
				}
			}
		} catch (error) {
			console.error('查询中奖结果失败:', error);
		} finally {
			// 重置请求状态
			this.isRequestingDrops = false;
		}
	}

	// 播放动画并在完成后播放attack动画
	private playAnimationWithCallback(animationName: string) {
		if (!this.animationManager) return;

		if (animationName === 'dajiang') {
			this.animationManager.playWinAnimation();
		} else if (animationName === 'zhadan') {
			this.animationManager.playLoseAnimation();
		} else {
			// 其他动画，播放完成后播放attack
			this.animationManager.playAnimationSequence([
				{ name: animationName, loop: false },
				{ name: 'attack', loop: true },
			]);
		}
	}

	// 启动中奖结果查询定时器
	startCheckDropsTimer() {
		if (this.timerManager && !this.isDestroyed) {
			// 先清理可能存在的定时器
			this.timerManager.clearInterval('checkDrops');

			this.timerManager.setInterval(
				'checkDrops',
				() => {
					// 在定时器回调中检查组件状态
					if (!this.isDestroyed) {
						this.checkDrops();
					}
				},
				10000
			); // 10秒 = 10000毫秒

			console.log('中奖结果查询定时器已启动');
		}
	}

	// 停止中奖结果查询定时器
	stopCheckDropsTimer() {
		if (this.timerManager) {
			this.timerManager.clearInterval('checkDrops');
		}
	}

	// 开始游戏逻辑
	private async startGame() {
		try {
			const { code, msg } = await request.post('/app-api/ppx/game/start');
			if (code === 200) {
				this.isPaused = false;
				if (this.Game_Label) this.Game_Label.string = '暂停';
				this.startTimer();
				// 启动中奖结果查询定时器
				this.startCheckDropsTimer();
				// 播放攻击动画
				if (this.animationManager) {
					this.animationManager.playAttackAnimation();
				}
				// 播放背景音乐
				if (this.audioManager) {
					this.audioManager.playBackgroundMusic();
				}

				// 隐藏游戏开始节点
				if (this.Game_Start_Node) this.Game_Start_Node.active = false;
				if (this.Game_Pause_Node) this.Game_Pause_Node.active = true;
			} else {
				this.showMessage(msg || '开始游戏失败');
			}
		} catch (error) {
			console.error('开始游戏失败:', error);
			this.showMessage('开始游戏失败');
		}
	}

	// 暂停游戏逻辑
	private async pauseGame() {
		try {
			const { code, msg } = await request.post('/app-api/ppx/game/pause');
			if (code === 200) {
				this.isPaused = true;
				if (this.Game_Label) this.Game_Label.string = '开始';

				// 只有当剩余游戏时长currentGameRemainingTime没有时才会执行以下操作
				if (this.currentGameRemainingTime <= 0) {
					// 停止中奖结果查询定时器
					this.stopCheckDropsTimer();

					// 播放睡觉动画
					if (this.animationManager) {
						this.animationManager.playSleepAnimation();
					}
					// 停止背景音乐
					if (this.audioManager) {
						this.audioManager.stopBackgroundMusic();
					}
				}
			} else {
				this.showMessage(msg || '暂停游戏失败');
			}
		} catch (error) {
			console.error('暂停游戏失败:', error);
			this.showMessage('暂停游戏失败');
		}
	}

	// 倒计时
	startTimer() {
		if (
			this.timerManager &&
			!this.isDestroyed &&
			!this.timerManager.hasInterval('gameTimer')
		) {
			this.timerManager.setInterval(
				'gameTimer',
				() => {
					// 在定时器回调中检查组件状态
					if (this.isDestroyed) {
						this.stopTimer();
						return;
					}

					if (this.timeLeft > 0) {
						this.timeLeft -= 1;

						// 优先消耗本轮游戏剩余时长
						if (this.currentGameRemainingTime > 0) {
							this.currentGameRemainingTime -= 1;
						} else if (this.totalPlayableTime > 0) {
							this.totalPlayableTime -= 1;
						}

						if (this.timeLeft < 0) this.timeLeft = 0;
						if (this.currentGameRemainingTime < 0)
							this.currentGameRemainingTime = 0;
						if (this.totalPlayableTime < 0) this.totalPlayableTime = 0;

						if (this.Time_Label) {
							// 如果暂停了，并且currentGameRemainingTime>0，那么只显示currentGameRemainingTime剩余时长
							if (this.isPaused && this.currentGameRemainingTime > 0) {
								this.Time_Label.string = this.formatTime(
									this.currentGameRemainingTime
								);
							} else {
								this.Time_Label.string = this.formatTime(this.timeLeft);
							}
						}

						// 检查：如果本轮游戏剩余时间用完且游戏处于暂停状态，则停止倒计时
						if (this.currentGameRemainingTime <= 0 && this.isPaused) {
							this.stopTimer();
						}
					} else {
						this.stopTimer();
					}
				},
				1000
			);
		}
	}

	stopTimer() {
		if (this.timerManager) {
			this.timerManager.clearInterval('gameTimer');
			this.timerManager.clearInterval('refreshData');
		}

		// 同时停止中奖结果查询定时器
		this.stopCheckDropsTimer();
		// 播放睡觉动画
		if (this.animationManager) {
			this.animationManager.playSleepAnimation();
		}
		// 停止背景音乐
		if (this.audioManager) {
			this.audioManager.stopBackgroundMusic();
		}
	}

	refreshGameData() {
		// 获取游戏数据，启用游戏状态检查
		this.getGameData(true);
		if (this.timerManager && !this.isDestroyed) {
			// 先清理可能存在的定时器
			this.timerManager.clearInterval('refreshData');

			this.timerManager.setInterval(
				'refreshData',
				() => {
					// 在定时器回调中检查组件状态
					if (!this.isDestroyed) {
						this.getGameData();
					}
				},
				5000
			);

			console.log('游戏数据刷新定时器已启动');
		}
	}

	start() {
		// 初始化管理器
		this.initializeManagers();

		// 初始化数字动画工具
		// this.apAnimation = new NumberAnimationUtil(this.Ap_Label, 2); // 体力值显示保留2位小数
		// this.manaAnimation = new NumberAnimationUtil(this.Mana_Label, 2); // 法力值显示保留2位小数
		// this.pointsAnimation = new NumberAnimationUtil(this.Points_Label, 2); // 积分显示保留2位小数
		this.Game_Start_Node.active = false;

		// 初始化音乐状态
		this.isMusicEnabled = MusicStorageUtil.getMusicEnabled();

		// 根据时间差判断是否过期，设置初始动画
		this.setInitialAnimationByTimeDiff();

		this.getInfo();
		this.refreshGameData();

		// 初始化列表选中功能
		this.initListSelection();

		// 初始化音乐UI状态
		this.initMusicUI();

		// 预加载JC场景
		this.preloadJCScene();
	}

	/**
	 * 初始化管理器
	 */
	private initializeManagers(): void {
		// 初始化定时器管理器
		this.timerManager = createTimerManager();

		// 初始化动画管理器
		if (this.Panda_Spine) {
			this.animationManager = createAnimationManager(this.Panda_Spine);
		}

		// 初始化音频管理器
		this.audioManager = createAudioManager();

		// 注册音频源
		if (this.audioManager) {
			// 注册背景音乐
			if (this.GB_Node) {
				this.audioManager.registerAudioFromNode(
					AudioType.BACKGROUND,
					this.GB_Node,
					0.5,
					true
				);
			}

			// 注册中奖音效
			if (this.Game_Pause_Node) {
				this.audioManager.registerAudioFromNode(
					AudioType.WIN_EFFECT,
					this.Game_Pause_Node,
					1.0,
					false
				);
			}

			// 注册失败音效
			if (this.Panda_Spine) {
				this.audioManager.registerAudioFromNode(
					AudioType.LOSE_EFFECT,
					this.Panda_Spine.node,
					0.6,
					false
				);
			}
		}

		// 注册场景切换清理回调
		this.sceneCleanupCallback = () => {
			console.log('场景切换前清理Info组件资源');
			this.cleanupBeforeSceneTransition();
		};
		registerSceneCleanup(this.sceneCleanupCallback);

		console.log('所有管理器初始化完成');
	}

	/**
	 * 场景切换前的清理工作
	 */
	private cleanupBeforeSceneTransition(): void {
		// 立即标记为已销毁
		this.isDestroyed = true;

		// 停止所有定时器
		this.stopTimer();

		// 停止所有音频
		if (this.audioManager) {
			this.audioManager.stopAllAudio();
		}

		console.log('Info组件场景切换前清理完成');
	}

	// 根据时间差判断是否过期，设置初始动画
	private setInitialAnimationByTimeDiff() {
		if (!this.animationManager) return;

		// 从本地存储获取离开时间和剩余时间
		const savedData = LocalStorageUtil.getItem('gameTimeData', {
			leaveTime: 0,
			currentGameRemainingTime: 0,
		});

		const currentTime = Date.now();
		const timeDiff = (currentTime - savedData.leaveTime) / 1000; // 转换为秒

		// 如果剩余时间为0，或者时间差大于剩余时间，说明已过期，播放shuijiao
		if (
			savedData.currentGameRemainingTime <= 0 ||
			timeDiff >= savedData.currentGameRemainingTime
		) {
			this.animationManager.playAnimation('shuijiao', true);
		} else {
			// 否则播放attack
			this.animationManager.playAttackAnimation();
		}
	}

	// 预加载JC场景
	private preloadJCScene() {
		// 预加载JC场景
		director.preloadScene(
			'JC',
			(completedCount: number, totalCount: number, _item: any) => {
				// 可以在这里添加加载进度显示
				console.log(`预加载JC场景进度: ${completedCount}/${totalCount}`);
			},
			(error: any) => {
				if (error) {
					console.error('预加载JC场景失败:', error);
				} else {
					console.log('JC场景预加载完成');
				}
			}
		);
	}

	// 初始化列表选中功能
	initListSelection() {
		// 获取content节点下的所有item
		const contentNode =
			this.TiLi_Node.getChildByName('box').getChildByName('content');

		// 获取所有item节点
		for (let i = 1; i <= 6; i++) {
			const itemNode = contentNode.getChildByName(`item${i}`);
			if (itemNode) {
				this.listItems.push(itemNode);
				// 为每个item添加点击事件
				let button = itemNode.getComponent(Button);
				if (!button) {
					// 如果没有Button组件，添加一个
					button = itemNode.addComponent(Button);
				}

				// 设置点击事件
				button.node.on(
					Button.EventType.CLICK,
					() => {
						this.setSelectedItem(i - 1); // 传入索引（0-5）
					},
					this
				);
			} else {
				console.warn(`找不到item${i}节点`);
			}
		}

		// 尝试从场景中获取背景图片
		this.getBackgroundSpritesFromScene();

		// 设置默认选中第一个
		this.setSelectedItem(0);
	}

	// 从场景中获取背景图片
	getBackgroundSpritesFromScene() {
		// 尝试从第一个item的bg3和bg4节点获取SpriteFrame
		if (this.listItems.length > 0) {
			const firstItem = this.listItems[0];
			const bg3Node = firstItem.getChildByName('bg3');
			const bg4Node = firstItem.getChildByName('bg4');
			const bg3Sprite = bg3Node.getComponent(Sprite);
			const bg4Sprite = bg4Node.getComponent(Sprite);
			this.bg3SpriteFrame = bg3Sprite.spriteFrame;
			this.bg4SpriteFrame = bg4Sprite.spriteFrame;
		}
	}

	// 设置选中的item
	setSelectedItem(index: number) {
		if (index < 0 || index >= this.listItems.length) {
			console.error('无效的索引:', index);
			return;
		}

		// 更新选中索引
		this.selectedIndex = index;

		// 更新所有item的背景
		for (let i = 0; i < this.listItems.length; i++) {
			const itemNode = this.listItems[i];
			if (!itemNode) continue;

			const bgNode =
				itemNode.getChildByName('bg3') || itemNode.getChildByName('bg4');
			if (!bgNode) continue;

			const sprite = bgNode.getComponent(Sprite);
			if (!sprite) continue;

			if (i === index) {
				// 选中状态，使用bg3
				if (this.bg3SpriteFrame) {
					sprite.spriteFrame = this.bg3SpriteFrame;
				}
				// 获取兑换的体力数量
				const M_Number_Label = itemNode.getChildByName('M-Number');
				if (M_Number_Label) {
					const label = M_Number_Label.getComponent(Label);
					if (label) {
						this.DHamount = parseInt(label.string) || 10;
						if (this.D_Number_Label) {
							this.D_Number_Label.string = this.DHamount.toString();
						}
					}
				}
			} else {
				// 未选中状态，使用bg4
				if (this.bg4SpriteFrame) {
					sprite.spriteFrame = this.bg4SpriteFrame;
				}
			}
		}
	}
	async exchangeTili() {
		try {
			// 先查询兑换比例
			const { data: rateData, code: rateCode } = await request.get(
				'/app-api/ppx/game/ap-exchange-rate'
			);
			if (rateCode !== 200) {
				this.showMessage('获取兑换比例失败');
				return;
			}

			const rate = rateData.rate || 1; // 兑换比例，默认1
			const isDiscount = rateData.isDiscount || false; // 是否折扣
			const cost = Number(this.DHamount); // 基础兑换数量

			// 只有在有折扣时才应用兑换比例
			const actualAmount = isDiscount ? Math.floor(cost * rate) : cost;

			const data = {
				amount: actualAmount, // 实际兑换的体力数
				cost: cost, // 花费的积分数
			};

			const { code, msg } = await request.post(
				'/app-api/ppx/game/exchange',
				data
			);
			if (code === 200) {
				// 兑换成功，显示提示信息
				this.showMessage('兑换成功！');
				// 可根据需要刷新体力、积分等数据
				this.getGameData();
			} else {
				// 兑换失败，显示提示信息
				this.showMessage(msg || '兑换失败');
			}
		} catch (error) {
			this.showMessage('兑换失败: ' + error);
		}
	}

	// 显示消息提示
	showMessage(message: string) {
		if (this.Message_Label) {
			this.Message_Label.string = message;
		}
		if (this.Message_Node) {
			this.Message_Node.active = true;

			// 2秒后隐藏消息
			if (this.timerManager) {
				this.timerManager.setTimeout(
					'hideMessage',
					() => {
						if (this.Message_Node) {
							this.Message_Node.active = false;
						}
					},
					2000
				);
			}
		}
	}

	// 获取当前选中的索引
	getSelectedIndex(): number {
		return this.selectedIndex;
	}

	// 显示体力值节点
	showTiLiNode() {
		this.TiLi_Node.active = true;
	}

	// 隐藏体力值节点
	hideTiLiNode() {
		this.TiLi_Node.active = false;
	}

	formatTime(totalSeconds: number): string {
		const hours = Math.floor(totalSeconds / 3600);
		const minutes = Math.floor((totalSeconds % 3600) / 60);
		const seconds = totalSeconds % 60;
		const pad = (n: number) => {
			const str = n.toString();
			return str.length < 2 ? '0' + str : str;
		};
		return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
	}

	async loadRemoteImage(url: string) {
		try {
			// 使用ImageAsset加载远程图片
			const imageAsset = await this.loadImageAsset(url);
			if (imageAsset && this.User_Avatar) {
				// 设置到Sprite组件
				this.User_Avatar.spriteFrame = SpriteFrame.createWithImage(imageAsset);
				// 设置图片大小
				if (this.User_Avatar.node) {
					this.User_Avatar.node.setScale(0.5, 0.5); // 使用缩放来调整大小
				}
			}
		} catch (error) {
			console.error('Failed to load remote image:', error);
		}
	}

	loadImageAsset(url: string): Promise<ImageAsset> {
		return new Promise((resolve, reject) => {
			assetManager.loadRemote<ImageAsset>(url, (err, imageAsset) => {
				if (err) {
					reject(err);
					return;
				}
				resolve(imageAsset);
			});
		});
	}

	// 播放Panda睡觉动画（已迁移到AnimationManager）
	// playPandaSleepAnimation() {
	//   if (this.animationManager) {
	//     this.animationManager.playSleepAnimation();
	//   }
	// }

	// 播放Panda攻击动画（已迁移到AnimationManager）
	// playPandaAttackAnimation() {
	//   if (this.animationManager) {
	//     // 从本地存储获取离开时间和剩余时间
	//     const savedData = LocalStorageUtil.getItem('gameTimeData', {
	//       leaveTime: 0,
	//       currentGameRemainingTime: 0,
	//     });
	//
	//     const currentTime = Date.now();
	//     const timeDiff = (currentTime - savedData.leaveTime) / 1000; // 转换为秒
	//
	//     // 如果剩余时间大于0且时间差小于剩余时间，直接播放attack动画
	//     if (
	//       savedData.currentGameRemainingTime > 0 &&
	//       timeDiff < savedData.currentGameRemainingTime
	//     ) {
	//       this.animationManager.playAttackAnimation();
	//     } else {
	//       this.animationManager.playWakeUpAnimation();
	//     }
	//   }
	// }

	// 播放背景音乐（已迁移到AudioManager）
	// playBackgroundMusic() {
	//   if (this.audioManager) {
	//     this.audioManager.playBackgroundMusic();
	//   }
	// }

	// 停止背景音乐（已迁移到AudioManager）
	// stopBackgroundMusic() {
	//   if (this.audioManager) {
	//     this.audioManager.stopBackgroundMusic();
	//   }
	// }

	update(_deltaTime: number) {
		// 更新数字动画
		// if (this.apAnimation) {
		//   this.apAnimation.update(deltaTime);
		// }
		// if (this.manaAnimation) {
		//   this.manaAnimation.update(deltaTime);
		// }
		// if (this.pointsAnimation) {
		//   this.pointsAnimation.update(deltaTime);
		// }
	}

	// 音乐开关状态
	private isMusicEnabled: boolean = true;

	// 控制背景音乐开关
	toggleBackgroundMusic(event: any) {
		// 获取当前点击的节点
		const currentNode = event.target || event.currentTarget;

		// 使用AudioManager切换音乐状态
		if (this.audioManager) {
			const newState = this.audioManager.toggleMusic();
			this.isMusicEnabled = newState;

			// 如果开启音乐且游戏正在运行，播放背景音乐
			if (newState && this.currentGameRemainingTime > 0) {
				this.audioManager.playBackgroundMusic();
			}
		}

		// 更新UI显示
		this.updateMusicUI(currentNode);
	}

	// 更新音乐UI显示
	private updateMusicUI(currentNode?: Node) {
		const musicNode = currentNode;
		if (musicNode) {
			const bg20Node = musicNode.getChildByName('bg20'); // 开启音乐显示
			const bg19Node = musicNode.getChildByName('bg19'); // 关闭音乐显示

			if (bg20Node && bg19Node) {
				if (this.isMusicEnabled) {
					// 音乐开启状态：显示bg20，隐藏bg19
					bg20Node.active = true;
					bg19Node.active = false;
				} else {
					// 音乐关闭状态：显示bg19，隐藏bg20
					bg20Node.active = false;
					bg19Node.active = true;
				}
			}
		}
	}

	// 初始化音乐UI状态
	private initMusicUI() {
		// 初始化时更新所有音乐控制按钮的UI状态
		this.updateMusicUI(this.Music_Node);
	}

	// 页面离开时保存当前时间
	onDestroy() {
		// 立即标记为已销毁，防止后续操作
		this.isDestroyed = true;

		console.log('Info组件开始销毁...');

		this.stopTimer();

		// 销毁管理器
		if (this.timerManager) {
			this.timerManager.destroy();
			this.timerManager = null;
		}

		if (this.animationManager) {
			this.animationManager.destroy();
			this.animationManager = null;
		}

		if (this.audioManager) {
			this.audioManager.destroy();
			this.audioManager = null;
		}

		// 重置请求状态
		this.isRequestingGameData = false;
		this.isRequestingDrops = false;

		// 移除场景切换清理回调
		if (this.sceneCleanupCallback) {
			unregisterSceneCleanup(this.sceneCleanupCallback);
			this.sceneCleanupCallback = null;
		}

		// 保存当前时间到本地存储
		LocalStorageUtil.setItem('gameTimeData', {
			leaveTime: Date.now(),
			currentGameRemainingTime: this.currentGameRemainingTime,
		});

		console.log('Info组件已销毁，所有管理器已清理');
	}
}

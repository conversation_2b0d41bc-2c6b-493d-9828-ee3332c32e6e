# 管理器使用指南

本文档介绍如何使用重构后的定时器、动画和音频管理器。

## 概述

重构后的代码将原本分散在各个组件中的定时器、动画播放和声音播放功能抽离到了统一的管理器中：

- **TimerManager**: 统一管理定时器和延时器
- **AnimationManager**: 统一管理Spine动画播放
- **AudioManager**: 统一管理音频播放和控制
- **NumberAnimationManager**: 统一管理数字动画

## TimerManager 使用方法

### 基本用法

```typescript
import { TimerManager, createTimerManager } from './TimerManager';

// 创建定时器管理器实例
private timerManager: TimerManager = createTimerManager();

// 设置定时器
this.timerManager.setInterval('myTimer', () => {
    console.log('定时器执行');
}, 1000);

// 设置延时器
this.timerManager.setTimeout('myTimeout', () => {
    console.log('延时器执行');
}, 2000);

// 清理定时器
this.timerManager.clearInterval('myTimer');
this.timerManager.clearTimeout('myTimeout');

// 销毁管理器（在onDestroy中调用）
this.timerManager.destroy();
```

### 主要方法

- `setInterval(key, callback, interval)`: 创建定时器
- `setTimeout(key, callback, delay)`: 创建延时器
- `clearInterval(key)`: 清理指定定时器
- `clearTimeout(key)`: 清理指定延时器
- `clearAll()`: 清理所有定时器和延时器
- `destroy()`: 销毁管理器

## AnimationManager 使用方法

### 基本用法

```typescript
import { AnimationManager, createAnimationManager } from './AnimationManager';

// 创建动画管理器实例
private animationManager: AnimationManager = createAnimationManager(this.spineComponent);

// 播放单个动画
this.animationManager.playAnimation('attack', true); // 循环播放

// 播放动画序列
this.animationManager.playAnimationSequence([
    { name: 'end', loop: false },
    { name: 'shuijiao', loop: true }
]);

// 预定义的动画方法
this.animationManager.playAttackAnimation();
this.animationManager.playSleepAnimation();
this.animationManager.playWinAnimation();
this.animationManager.playLoseAnimation();

// 销毁管理器
this.animationManager.destroy();
```

### 主要方法

- `playAnimation(name, loop, onComplete, onStart)`: 播放单个动画
- `playAnimationSequence(animations)`: 播放动画序列
- `playAttackAnimation()`: 播放攻击动画
- `playSleepAnimation()`: 播放睡觉动画序列
- `playWinAnimation(onComplete)`: 播放中奖动画
- `playLoseAnimation(onComplete)`: 播放失败动画
- `stopAnimation()`: 停止当前动画
- `getCurrentAnimation()`: 获取当前动画名称
- `isAnimationPlaying()`: 检查是否正在播放动画

## AudioManager 使用方法

### 基本用法

```typescript
import { AudioManager, AudioType, createAudioManager } from './AudioManager';

// 创建音频管理器实例
private audioManager: AudioManager = createAudioManager();

// 注册音频源
this.audioManager.registerAudioFromNode(AudioType.BACKGROUND, this.bgMusicNode, 0.5, true);
this.audioManager.registerAudioFromNode(AudioType.WIN_EFFECT, this.winEffectNode, 1.0, false);

// 播放音频
this.audioManager.playBackgroundMusic();
this.audioManager.playWinEffect();
this.audioManager.playLoseEffect();

// 控制音乐开关
this.audioManager.toggleMusic();
this.audioManager.setMusicEnabled(true);

// 音量控制
this.audioManager.setVolume(AudioType.BACKGROUND, 0.8);
this.audioManager.setMasterVolume(0.9);

// 销毁管理器
this.audioManager.destroy();
```

### 主要方法

- `registerAudioFromNode(type, node, volume, loop)`: 注册音频源
- `playAudio(type, forcePlay)`: 播放指定类型音频
- `stopAudio(type)`: 停止指定类型音频
- `toggleMusic()`: 切换音乐开关状态
- `setMusicEnabled(enabled)`: 设置音乐开关状态
- `setVolume(type, volume)`: 设置指定音频音量
- `setMasterVolume(volume)`: 设置主音量

## 在组件中的完整使用示例

```typescript
import { _decorator, Component, Node, sp, AudioSource } from 'cc';
import { TimerManager, createTimerManager } from './TimerManager';
import { AnimationManager, createAnimationManager } from './AnimationManager';
import { AudioManager, AudioType, createAudioManager } from './AudioManager';

@ccclass('MyComponent')
export class MyComponent extends Component {
    @property(sp.Skeleton)
    spineNode: sp.Skeleton = null;

    @property(Node)
    bgMusicNode: Node = null;

    private timerManager: TimerManager | null = null;
    private animationManager: AnimationManager | null = null;
    private audioManager: AudioManager | null = null;

    start() {
        this.initializeManagers();
    }

    private initializeManagers(): void {
        // 初始化定时器管理器
        this.timerManager = createTimerManager();

        // 初始化动画管理器
        if (this.spineNode) {
            this.animationManager = createAnimationManager(this.spineNode);
        }

        // 初始化音频管理器
        this.audioManager = createAudioManager();
        if (this.bgMusicNode) {
            this.audioManager.registerAudioFromNode(
                AudioType.BACKGROUND, 
                this.bgMusicNode, 
                0.5, 
                true
            );
        }
    }

    onDestroy() {
        // 清理所有管理器
        if (this.timerManager) {
            this.timerManager.destroy();
            this.timerManager = null;
        }

        if (this.animationManager) {
            this.animationManager.destroy();
            this.animationManager = null;
        }

        if (this.audioManager) {
            this.audioManager.destroy();
            this.audioManager = null;
        }
    }
}
```

## 注意事项

1. **内存管理**: 务必在组件的`onDestroy`方法中调用管理器的`destroy()`方法
2. **定时器键名**: 使用有意义的键名来标识不同的定时器，避免冲突
3. **音频注册**: 在使用音频功能前，必须先注册音频源
4. **动画管理**: AnimationManager需要有效的Spine组件才能正常工作
5. **错误处理**: 管理器内部已包含错误处理，但建议在使用前检查管理器是否已正确初始化

## 迁移指南

### 从旧代码迁移

1. **定时器迁移**:
   ```typescript
   // 旧代码
   this.timer = setInterval(() => {}, 1000);
   clearInterval(this.timer);

   // 新代码
   this.timerManager.setInterval('timer', () => {}, 1000);
   this.timerManager.clearInterval('timer');
   ```

2. **动画迁移**:
   ```typescript
   // 旧代码
   this.spine.setAnimation(0, 'attack', true);

   // 新代码
   this.animationManager.playAttackAnimation();
   ```

3. **音频迁移**:
   ```typescript
   // 旧代码
   this.audioSource.play();

   // 新代码
   this.audioManager.playBackgroundMusic();
   ```

通过使用这些管理器，代码将更加模块化、易于维护，并且能够有效避免内存泄漏问题。

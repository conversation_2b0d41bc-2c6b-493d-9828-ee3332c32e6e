{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "cocos-mcp", "version": "1.0.0", "author": "myl", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "tsc", "watch": "tsc -w"}, "description": "i18n:cocos-mcp.description", "main": "./dist/main.js", "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "contributions": {"menu": [{"path": "i18n:cocos-mcp", "label": "i18n:cocos-mcp.show_log", "message": "show-log"}], "messages": {"show-log": {"methods": ["showLog"]}}}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.1", "zod": "^3.25.76"}}
import { Mcp<PERSON>ridge } from './mcp-bridge';

/**
 * MCP桥接实例
 */
let mcpBridge: McpBridge | null = null;

/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
export const methods: { [key: string]: (...any: any) => any } = {
	/**
	 * @en A method that can be triggered by message
	 * @zh 通过 message 触发的方法
	 */
	showLog() {
		console.log('Hello World from Cocos MCP Extension');
	},

	/**
	 * 创建节点
	 */
	async createNode(nodeData: any) {
		try {
			const result = await Editor.Message.request(
				'scene',
				'create-node',
				nodeData
			);
			console.log('Node created:', result);
			return { success: true, result };
		} catch (error) {
			console.error('Failed to create node:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 删除节点
	 */
	async deleteNode(nodeUuid: string) {
		try {
			const result = await Editor.Message.request(
				'scene',
				'delete-node',
				nodeUuid
			);
			console.log('Node deleted:', result);
			return { success: true, result };
		} catch (error) {
			console.error('Failed to delete node:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 修改节点属性
	 */
	async setNodeProperty(nodeUuid: string, path: string, value: any) {
		try {
			const result = await Editor.Message.request('scene', 'set-property', {
				uuid: nodeUuid,
				path,
				value: value,
			} as any);
			console.log('Node property set:', result);
			return { success: true, result };
		} catch (error) {
			console.error('Failed to set node property:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 添加组件
	 */
	async addComponent(nodeUuid: string, componentType: string) {
		try {
			const result = await Editor.Message.request('scene', 'create-component', {
				uuid: nodeUuid,
				component: componentType,
			});
			console.log('Component added:', result);
			return { success: true, result };
		} catch (error) {
			console.error('Failed to add component:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 获取场景信息
	 */
	async getSceneInfo() {
		try {
			const result = await Editor.Message.request('scene', 'query-scene');
			console.log('Scene info:', result);
			return { success: true, result };
		} catch (error) {
			console.error('Failed to get scene info:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 获取节点信息
	 */
	async getNodeInfo(nodeUuid: string) {
		try {
			const result = await Editor.Message.request(
				'scene',
				'query-node',
				nodeUuid
			);
			console.log('Node info:', result);
			return { success: true, result };
		} catch (error) {
			console.error('Failed to get node info:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 刷新资源
	 */
	async refreshAssets() {
		try {
			const result = await Editor.Message.request('asset-db', 'refresh');
			console.log('Assets refreshed:', result);
			return { success: true, result };
		} catch (error) {
			console.error('Failed to refresh assets:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 启动MCP桥接服务
	 */
	async startMcpBridge() {
		try {
			if (!mcpBridge) {
				mcpBridge = new McpBridge();
				await mcpBridge.start();
			}
			return { success: true, message: 'MCP Bridge started' };
		} catch (error) {
			console.error('Failed to start MCP Bridge:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},

	/**
	 * 停止MCP桥接服务
	 */
	async stopMcpBridge() {
		try {
			if (mcpBridge) {
				await mcpBridge.stop();
				mcpBridge = null;
			}
			return { success: true, message: 'MCP Bridge stopped' };
		} catch (error) {
			console.error('Failed to stop MCP Bridge:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			};
		}
	},
};

/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
export function load() {
	console.log('Cocos MCP Extension loaded');
	// 自动启动MCP桥接服务
	methods.startMcpBridge();
}

/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
export function unload() {
	console.log('Cocos MCP Extension unloaded');
	// 停止MCP桥接服务
	if (mcpBridge) {
		mcpBridge.stop();
		mcpBridge = null;
	}
}

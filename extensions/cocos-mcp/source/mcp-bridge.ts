import * as http from 'http';
import * as net from 'net';

/**
 * MCP桥接类
 * 负责在Cocos Creator扩展和MCP服务器之间建立通信桥梁
 */
export class McpBridge {
    private server: http.Server | null = null;
    private port: number = 3001;
    private isRunning: boolean = false;

    constructor(port: number = 3001) {
        this.port = port;
    }

    /**
     * 启动桥接服务
     */
    async start(): Promise<void> {
        if (this.isRunning) {
            console.log('MCP Bridge is already running');
            return;
        }

        return new Promise((resolve, reject) => {
            this.server = http.createServer((req, res) => {
                this.handleRequest(req, res);
            });

            this.server.listen(this.port, () => {
                this.isRunning = true;
                console.log(`MCP Bridge started on port ${this.port}`);
                resolve();
            });

            this.server.on('error', (error) => {
                console.error('MCP Bridge server error:', error);
                reject(error);
            });
        });
    }

    /**
     * 停止桥接服务
     */
    async stop(): Promise<void> {
        if (!this.isRunning || !this.server) {
            return;
        }

        return new Promise((resolve) => {
            this.server!.close(() => {
                this.isRunning = false;
                this.server = null;
                console.log('MCP Bridge stopped');
                resolve();
            });
        });
    }

    /**
     * 处理HTTP请求
     */
    private async handleRequest(req: http.IncomingMessage, res: http.ServerResponse): Promise<void> {
        // 设置CORS头
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }

        if (req.method === 'POST' && req.url === '/editor-action') {
            await this.handleEditorAction(req, res);
        } else if (req.method === 'GET' && req.url === '/status') {
            this.handleStatusRequest(res);
        } else {
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Not found' }));
        }
    }

    /**
     * 处理编辑器操作请求
     */
    private async handleEditorAction(req: http.IncomingMessage, res: http.ServerResponse): Promise<void> {
        try {
            const body = await this.readRequestBody(req);
            const action = JSON.parse(body);

            console.log('Received editor action:', action);

            let result;
            switch (action.type) {
                case 'createNode':
                    result = await this.executeEditorMethod('createNode', action.data);
                    break;
                case 'deleteNode':
                    result = await this.executeEditorMethod('deleteNode', action.nodeUuid);
                    break;
                case 'setNodeProperty':
                    result = await this.executeEditorMethod('setNodeProperty', 
                        action.nodeUuid, action.path, action.value);
                    break;
                case 'addComponent':
                    result = await this.executeEditorMethod('addComponent', 
                        action.nodeUuid, action.componentType);
                    break;
                case 'getSceneInfo':
                    result = await this.executeEditorMethod('getSceneInfo');
                    break;
                case 'getNodeInfo':
                    result = await this.executeEditorMethod('getNodeInfo', action.nodeUuid);
                    break;
                case 'refreshAssets':
                    result = await this.executeEditorMethod('refreshAssets');
                    break;
                default:
                    throw new Error(`Unknown action type: ${action.type}`);
            }

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(result));

        } catch (error) {
            console.error('Error handling editor action:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ 
                success: false, 
                error: error instanceof Error ? error.message : 'Unknown error' 
            }));
        }
    }

    /**
     * 处理状态请求
     */
    private handleStatusRequest(res: http.ServerResponse): void {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
            status: 'running', 
            port: this.port,
            timestamp: new Date().toISOString()
        }));
    }

    /**
     * 读取请求体
     */
    private readRequestBody(req: http.IncomingMessage): Promise<string> {
        return new Promise((resolve, reject) => {
            let body = '';
            req.on('data', (chunk) => {
                body += chunk.toString();
            });
            req.on('end', () => {
                resolve(body);
            });
            req.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * 执行编辑器方法
     * 这里需要通过某种方式调用主进程的方法
     */
    private async executeEditorMethod(methodName: string, ...args: any[]): Promise<any> {
        try {
            // 这里我们需要找到一种方式来调用主进程的方法
            // 由于我们在同一个进程中，可以直接导入并调用
            const { methods } = await import('./main');
            
            if (typeof methods[methodName] === 'function') {
                return await methods[methodName](...args);
            } else {
                throw new Error(`Method ${methodName} not found`);
            }
        } catch (error) {
            console.error(`Error executing method ${methodName}:`, error);
            throw error;
        }
    }

    /**
     * 检查端口是否可用
     */
    private isPortAvailable(port: number): Promise<boolean> {
        return new Promise((resolve) => {
            const server = net.createServer();
            server.listen(port, () => {
                server.close(() => {
                    resolve(true);
                });
            });
            server.on('error', () => {
                resolve(false);
            });
        });
    }

    /**
     * 获取可用端口
     */
    async getAvailablePort(startPort: number = 3001): Promise<number> {
        let port = startPort;
        while (!(await this.isPortAvailable(port))) {
            port++;
            if (port > 65535) {
                throw new Error('No available port found');
            }
        }
        return port;
    }
}

# Cocos Creator MCP服务器设置指南

## 🎉 恭喜！MCP服务器已成功创建

您的Cocos Creator项目现在已经集成了MCP（Model Context Protocol）服务器，可以与AI大模型进行深度交互。

## 📋 已完成的工作

✅ **MCP服务器核心功能**
- 资源访问：项目信息、场景、资源文件、代码等
- 开发工具：文件操作、场景创建、组件生成、代码分析等
- 智能提示：开发指导、调试帮助、优化建议、学习路径等

✅ **项目结构**
```
mcp-server/
├── src/                     # 源代码
├── dist/                    # 编译输出
├── package.json            # 依赖配置
├── tsconfig.json           # TypeScript配置
├── start.sh                # 启动脚本
├── test-server.js          # 测试脚本
└── README.md               # 详细文档
```

✅ **测试验证**
- MCP协议通信正常
- 资源、工具、提示功能完整
- 服务器启动和响应正常

## 🚀 下一步：配置AI客户端

### 方法1：<PERSON> Desktop（推荐）

1. **找到配置文件**：
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - Linux: `~/.config/Claude/claude_desktop_config.json`

2. **添加配置**（替换为您的实际路径）：
```json
{
  "mcpServers": {
    "cocos-creator": {
      "command": "node",
      "args": ["/Users/<USER>/Documents/wb/cocos/cocos-ppx/mcp-server/dist/index.js"],
      "cwd": "/Users/<USER>/Documents/wb/cocos/cocos-ppx"
    }
  }
}
```

3. **重启Claude Desktop**

### 方法2：其他MCP客户端

使用以下命令启动服务器：
```bash
cd /Users/<USER>/Documents/wb/cocos/cocos-ppx/mcp-server
npm run start:stdio
```

## 🎯 使用示例

配置完成后，您可以在AI对话中使用以下功能：

### 📊 项目分析
```
请分析我的Cocos Creator项目结构和健康状况
```

### 🔧 组件开发
```
帮我创建一个PlayerController组件，包含移动、跳跃和射击功能
```

### 🎨 场景设计
```
我想设计一个2D横版游戏的主菜单场景，请给我建议
```

### 🐛 问题调试
```
我的游戏在移动设备上帧率很低，请帮我分析和优化
```

### 📚 学习指导
```
我是Cocos Creator初学者，请为我制定一个学习计划
```

## 🔧 管理和维护

### 启动服务器
```bash
# 方法1：使用npm脚本
cd mcp-server
npm run start:stdio

# 方法2：使用启动脚本
./mcp-server/start.sh

# 方法3：HTTP模式（用于Web集成）
npm run start:http
```

### 测试服务器
```bash
cd mcp-server
node test-server.js
```

### 更新和扩展
- 修改 `src/providers/` 下的文件来添加新功能
- 运行 `npm run build` 重新编译
- 重启AI客户端以应用更改

## 📖 功能概览

### 🗂️ 可访问的资源
- `cocos://project/info` - 项目基本信息
- `cocos://scenes/list` - 场景列表
- `cocos://scenes/{name}` - 特定场景详情
- `cocos://assets/{type}` - 资源文件（按类型）
- `cocos://code/{path}` - 源代码文件
- `cocos://config/settings` - 项目配置

### 🛠️ 可用的工具
- **文件操作**: create-file, read-file, modify-file
- **场景操作**: create-scene, analyze-scene
- **代码生成**: generate-component
- **项目分析**: project-health-check, analyze-code

### 💡 智能提示
- **开发**: develop-component, design-scene, implement-game-logic
- **调试**: debug-performance, diagnose-error
- **优化**: optimize-performance, optimize-assets
- **学习**: learning-path, best-practices

## 🆘 故障排除

### 常见问题

1. **"找不到模块"错误**
   ```bash
   cd mcp-server
   npm install
   npm run build
   ```

2. **权限错误**
   ```bash
   chmod +x mcp-server/start.sh
   ```

3. **路径问题**
   - 确保使用绝对路径
   - 检查cwd设置是否正确

4. **连接失败**
   - 检查Node.js版本（需要18+）
   - 确保在项目根目录运行

### 获取帮助
- 查看详细文档：`mcp-server/README.md`
- 运行测试：`node mcp-server/test-server.js`
- 检查日志：启动时查看stderr输出

## 🎊 开始使用

现在您可以：
1. 配置您的AI客户端
2. 开始与AI进行Cocos Creator项目的深度对话
3. 让AI帮助您开发、调试和优化游戏

祝您开发愉快！🚀
